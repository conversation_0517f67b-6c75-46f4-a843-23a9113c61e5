#!/usr/bin/env node

/**
 * 测试农场区块升级功能
 * 验证升级是否正确使用新的配置系统
 */

const jwt = require('jsonwebtoken');
const axios = require('axios');
require('dotenv').config();

async function testUpgradeFunctionality() {
  console.log('🧪 测试农场区块升级功能\n');

  const baseUrl = 'http://localhost:3456/api';
  const jwtSecret = process.env.JWT_SECRET_Wallet;
  
  if (!jwtSecret) {
    console.error('❌ 缺少 JWT_SECRET_Wallet 环境变量');
    return;
  }

  try {
    // 1. 生成测试JWT token
    console.log('1. 生成测试JWT token...');
    
    const testPayload = {
      walletId: 1,
      walletAddress: 'test_wallet_address',
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (60 * 60) // 1小时后过期
    };
    
    const token = jwt.sign(testPayload, jwtSecret);
    console.log(`✅ Token生成成功`);

    // 2. 获取配置数据作为对比
    console.log('\n2. 获取配置数据...');
    
    const configResponse = await axios.get(`${baseUrl}/admin/farm-config/current`);
    const configs = configResponse.data.data.configs;
    
    const level1Config = configs.find(c => c.grade === 1);
    const level2Config = configs.find(c => c.grade === 2);
    
    console.log('📋 等级1配置数据:');
    console.log(`   production: ${level1Config.production}`);
    console.log(`   cow: ${level1Config.cow}`);
    console.log(`   speed: ${level1Config.speed}`);
    console.log(`   cost: ${level1Config.cost}`);
    
    console.log('📋 等级2配置数据:');
    console.log(`   production: ${level2Config.production}`);
    console.log(`   cow: ${level2Config.cow}`);
    console.log(`   speed: ${level2Config.speed}`);
    console.log(`   cost: ${level2Config.cost}`);

    // 3. 获取升级前的农场区块状态
    console.log('\n3. 获取升级前的农场区块状态...');
    
    const beforeUpgradeResponse = await axios.get(`${baseUrl}/farm/farm-plots`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    const beforePlots = beforeUpgradeResponse.data.data.farmPlots;
    const firstPlot = beforePlots[0]; // 第一个区块（已解锁）
    
    console.log('📦 升级前第一个区块状态:');
    console.log(`   等级: ${firstPlot.level}`);
    console.log(`   牛奶产量: ${firstPlot.milkProduction}`);
    console.log(`   牛数量: ${firstPlot.barnCount}`);
    console.log(`   生产速度: ${firstPlot.productionSpeed}`);
    console.log(`   升级费用: ${firstPlot.upgradeCost}`);

    // 4. 验证升级前数据是否正确
    console.log('\n🔍 升级前数据验证:');
    const beforeProductionMatch = parseFloat(firstPlot.milkProduction) === level1Config.production;
    const beforeCowMatch = firstPlot.barnCount === level1Config.cow;
    const beforeSpeedMatch = firstPlot.productionSpeed === level1Config.speed;
    const beforeCostMatch = parseFloat(firstPlot.upgradeCost) === level1Config.cost;
    
    console.log(`   产量使用等级1配置: ${beforeProductionMatch ? '✅' : '❌'} (${firstPlot.milkProduction} = ${level1Config.production})`);
    console.log(`   牛数量使用等级1配置: ${beforeCowMatch ? '✅' : '❌'} (${firstPlot.barnCount} = ${level1Config.cow})`);
    console.log(`   速度使用等级1配置: ${beforeSpeedMatch ? '✅' : '❌'} (${firstPlot.productionSpeed} = ${level1Config.speed})`);
    console.log(`   升级费用使用等级1配置: ${beforeCostMatch ? '✅' : '❌'} (${firstPlot.upgradeCost} = ${level1Config.cost})`);

    // 5. 执行升级
    console.log('\n4. 执行农场区块升级...');
    
    try {
      const upgradeResponse = await axios.post(`${baseUrl}/farm/farm-plots/upgrade`, {
        plotNumber: 1
      }, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log(`✅ 升级成功: ${upgradeResponse.status}`);
      
      const upgradedPlot = upgradeResponse.data.data.farmPlot;
      
      console.log('\n📦 升级后第一个区块状态:');
      console.log(`   等级: ${upgradedPlot.level}`);
      console.log(`   牛奶产量: ${upgradedPlot.milkProduction}`);
      console.log(`   牛数量: ${upgradedPlot.barnCount}`);
      console.log(`   生产速度: ${upgradedPlot.productionSpeed}`);
      console.log(`   升级费用: ${upgradedPlot.upgradeCost}`);

      // 6. 验证升级后数据是否正确
      console.log('\n🔍 升级后数据验证:');

      // 获取升级后等级的配置
      const upgradedLevelConfig = configs.find(c => c.grade === upgradedPlot.level);

      const afterProductionMatch = parseFloat(upgradedPlot.milkProduction) === upgradedLevelConfig.production;
      const afterCowMatch = upgradedPlot.barnCount === upgradedLevelConfig.cow;
      const afterSpeedMatch = upgradedPlot.productionSpeed === upgradedLevelConfig.speed;
      const afterCostMatch = parseFloat(upgradedPlot.upgradeCost) === upgradedLevelConfig.cost;
      
      console.log(`   产量使用等级${upgradedPlot.level}配置: ${afterProductionMatch ? '✅' : '❌'} (${upgradedPlot.milkProduction} = ${upgradedLevelConfig.production})`);
      console.log(`   牛数量使用等级${upgradedPlot.level}配置: ${afterCowMatch ? '✅' : '❌'} (${upgradedPlot.barnCount} = ${upgradedLevelConfig.cow})`);
      console.log(`   速度使用等级${upgradedPlot.level}配置: ${afterSpeedMatch ? '✅' : '❌'} (${upgradedPlot.productionSpeed} = ${upgradedLevelConfig.speed})`);
      console.log(`   升级费用使用等级${upgradedPlot.level}配置: ${afterCostMatch ? '✅' : '❌'} (${upgradedPlot.upgradeCost} = ${upgradedLevelConfig.cost})`);

      // 7. 验证升级预览功能
      if (upgradedPlot.nextUpgradeGrowth && Object.keys(upgradedPlot.nextUpgradeGrowth).length > 0) {
        console.log('\n📈 升级预览验证:');
        const nextUpgrade = upgradedPlot.nextUpgradeGrowth;
        // 获取下一等级配置
        const nextLevelConfig = configs.find(c => c.grade === upgradedPlot.level + 1);

        console.log(`   下次升级后产量: ${nextUpgrade.nextMilkProduction}`);
        console.log(`   等级${upgradedPlot.level + 1}配置production: ${nextLevelConfig.production}`);

        const nextProductionMatch = nextUpgrade.nextMilkProduction === nextLevelConfig.production;
        console.log(`   升级预览使用等级${upgradedPlot.level + 1}配置: ${nextProductionMatch ? '✅' : '❌'}`);
      }

      // 8. 总结
      console.log('\n📋 升级功能验证总结:');
      if (afterProductionMatch && afterCowMatch && afterSpeedMatch && afterCostMatch) {
        console.log('🎉 升级功能完全正确！');
        console.log('✅ 升级后的所有属性都使用了正确的配置数据');
        console.log('✅ milkProduction 使用 production 字段');
        console.log('✅ upgradeCost 使用当前等级的 cost 字段');
      } else {
        console.log('❌ 发现升级功能问题，需要进一步检查');
      }

    } catch (upgradeError) {
      if (upgradeError.response) {
        console.error(`❌ 升级失败: ${upgradeError.response.status} - ${upgradeError.response.data.message || upgradeError.response.data}`);
        
        // 如果是GEM不足，我们可以提供一些信息
        if (upgradeError.response.data.message && upgradeError.response.data.message.includes('GEM不足')) {
          console.log('\n💡 提示: 升级失败是因为GEM不足，这是正常的业务逻辑');
          console.log('   可以通过管理员接口给用户添加GEM来测试升级功能');
        }
      } else {
        console.error('❌ 升级网络错误:', upgradeError.message);
      }
    }

  } catch (error) {
    if (error.response) {
      console.error(`❌ API错误: ${error.response.status} - ${error.response.data.message || error.response.data}`);
    } else {
      console.error('❌ 网络错误:', error.message);
    }
  }
}

// 主函数
async function main() {
  console.log('🚀 农场区块升级功能测试开始');
  console.log('='.repeat(50));
  
  await testUpgradeFunctionality();
  
  console.log('\n' + '='.repeat(50));
  console.log('📋 测试完成');
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(error => {
    console.error('❌ 脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = { testUpgradeFunctionality };
