#!/usr/bin/env node

/**
 * 给测试用户添加GEM的脚本
 */

const mysql = require('mysql2/promise');
require('dotenv').config();

async function addTestGems() {
  const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT) || 3669,
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASS || 'root',
    database: process.env.DB_NAME || 'wolf_fun_db'
  };

  let connection;
  
  try {
    console.log('🎯 给测试用户添加GEM...\n');
    
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');

    const walletId = 1;
    const gemsToAdd = 100000; // 添加10万GEM，足够升级测试

    // 先查询表结构
    console.log('🔍 查询user_wallets表结构...');
    const [columns] = await connection.execute('DESCRIBE user_wallets');
    console.log('📋 表结构:');
    columns.forEach(col => {
      console.log(`   ${col.Field}: ${col.Type}`);
    });

    // 查询当前用户数据
    const [userData] = await connection.execute(
      'SELECT * FROM user_wallets WHERE id = ?',
      [walletId]
    );

    if (userData.length === 0) {
      console.log(`❌ 找不到用户 walletId=${walletId}`);
      return;
    }

    const currentGems = parseFloat(userData[0].gem);
    console.log(`📊 当前GEM余额: ${currentGems}`);

    // 更新GEM余额
    await connection.execute(
      'UPDATE user_wallets SET gem = gem + ?, updatedAt = NOW() WHERE id = ?',
      [gemsToAdd, walletId]
    );

    // 查询更新后的余额
    const [newBalance] = await connection.execute(
      'SELECT gem FROM user_wallets WHERE id = ?',
      [walletId]
    );

    const newGems = parseFloat(newBalance[0].gem);
    console.log(`💎 添加GEM: ${gemsToAdd}`);
    console.log(`📊 新的GEM余额: ${newGems}`);
    console.log(`✅ GEM添加成功！`);

  } catch (error) {
    console.error('❌ 添加GEM失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  addTestGems();
}

module.exports = addTestGems;
