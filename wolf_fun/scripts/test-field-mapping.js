#!/usr/bin/env node

/**
 * 测试农场API中的字段映射修复
 */

const jwt = require('jsonwebtoken');
const axios = require('axios');
require('dotenv').config();

async function testFieldMapping() {
  console.log('🧪 测试农场API字段映射修复\n');

  const baseUrl = 'http://localhost:3456/api';
  const jwtSecret = process.env.JWT_SECRET_Wallet;
  
  if (!jwtSecret) {
    console.error('❌ 缺少 JWT_SECRET_Wallet 环境变量');
    return;
  }

  try {
    // 1. 生成测试JWT token
    console.log('1. 生成测试JWT token...');
    
    const testPayload = {
      walletId: 1,
      walletAddress: 'test_wallet_address',
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (60 * 60) // 1小时后过期
    };
    
    const token = jwt.sign(testPayload, jwtSecret);
    console.log(`✅ Token生成成功`);

    // 2. 获取配置数据作为对比
    console.log('\n2. 获取配置数据...');
    
    const configResponse = await axios.get(`${baseUrl}/admin/farm-config/current`);
    const configs = configResponse.data.data.configs;
    const level1Config = configs.find(c => c.grade === 1);
    
    console.log('📋 等级1配置数据:');
    console.log(`   production: ${level1Config.production}`);
    console.log(`   milk: ${level1Config.milk}`);
    console.log(`   cost: ${level1Config.cost}`);
    console.log(`   cow: ${level1Config.cow}`);
    console.log(`   speed: ${level1Config.speed}`);

    // 3. 测试农场区块API
    console.log('\n3. 测试农场区块API...');
    
    const farmPlotsResponse = await axios.get(`${baseUrl}/farm/farm-plots`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    console.log(`✅ 农场区块API: ${farmPlotsResponse.status}`);
    
    if (farmPlotsResponse.data.ok) {
      const farmPlots = farmPlotsResponse.data.data.farmPlots;
      const firstPlot = farmPlots[0]; // 第一个区块（已解锁）
      
      console.log('\n📦 第一个区块（已解锁）详情:');
      console.log(`   区块编号: ${firstPlot.plotNumber}`);
      console.log(`   等级: ${firstPlot.level}`);
      console.log(`   是否解锁: ${firstPlot.isUnlocked}`);
      console.log(`   牛数量: ${firstPlot.barnCount}`);
      console.log(`   牛奶产量: ${firstPlot.milkProduction}`);
      console.log(`   生产速度: ${firstPlot.productionSpeed}`);
      console.log(`   升级费用: ${firstPlot.upgradeCost}`);

      // 4. 验证字段映射
      console.log('\n🔍 字段映射验证:');
      
      // 验证 milkProduction 使用 production 字段
      const productionMatch = parseFloat(firstPlot.milkProduction) === level1Config.production;
      console.log(`   milkProduction 使用 production 字段: ${productionMatch ? '✅' : '❌'}`);
      console.log(`     API返回: ${firstPlot.milkProduction}`);
      console.log(`     配置production: ${level1Config.production}`);
      console.log(`     配置milk: ${level1Config.milk} (不应该使用)`);
      
      // 验证 upgradeCost 使用当前等级的 cost
      const upgradeCostMatch = parseFloat(firstPlot.upgradeCost) === level1Config.cost;
      console.log(`   upgradeCost 使用当前等级cost: ${upgradeCostMatch ? '✅' : '❌'}`);
      console.log(`     API返回: ${firstPlot.upgradeCost}`);
      console.log(`     等级1配置cost: ${level1Config.cost}`);
      
      // 验证其他字段
      const barnCountMatch = firstPlot.barnCount === level1Config.cow;
      const speedMatch = firstPlot.productionSpeed === level1Config.speed;
      
      console.log(`   barnCount 正确: ${barnCountMatch ? '✅' : '❌'} (${firstPlot.barnCount} = ${level1Config.cow})`);
      console.log(`   productionSpeed 正确: ${speedMatch ? '✅' : '❌'} (${firstPlot.productionSpeed} = ${level1Config.speed})`);

      // 5. 验证升级预览
      if (firstPlot.nextUpgradeGrowth && Object.keys(firstPlot.nextUpgradeGrowth).length > 0) {
        console.log('\n📈 升级预览验证:');
        const nextUpgrade = firstPlot.nextUpgradeGrowth;
        
        // 获取等级2配置
        const level2Config = configs.find(c => c.grade === 2);
        
        console.log(`   下次升级后产量: ${nextUpgrade.nextMilkProduction}`);
        console.log(`   等级2配置production: ${level2Config.production}`);
        console.log(`   产量增长: ${nextUpgrade.milkProductionGrowth}`);
        console.log(`   计算验证: ${level2Config.production} - ${level1Config.production} = ${level2Config.production - level1Config.production}`);
        
        const nextProductionMatch = nextUpgrade.nextMilkProduction === level2Config.production;
        const growthMatch = Math.abs(nextUpgrade.milkProductionGrowth - (level2Config.production - level1Config.production)) < 0.001;
        
        console.log(`   升级预览使用production字段: ${nextProductionMatch ? '✅' : '❌'}`);
        console.log(`   产量增长计算正确: ${growthMatch ? '✅' : '❌'}`);
      }

      // 6. 总结
      console.log('\n📋 字段映射修复总结:');
      if (productionMatch && upgradeCostMatch && barnCountMatch && speedMatch) {
        console.log('🎉 所有字段映射都正确！');
        console.log('✅ milkProduction 现在使用 production 字段');
        console.log('✅ upgradeCost 现在使用当前等级的 cost 字段');
        console.log('✅ 其他字段映射也都正确');
      } else {
        console.log('❌ 发现字段映射问题，需要进一步检查');
      }

    } else {
      console.log(`❌ API返回错误: ${farmPlotsResponse.data.message}`);
    }

  } catch (error) {
    if (error.response) {
      console.error(`❌ API错误: ${error.response.status} - ${error.response.data.message || error.response.data}`);
    } else {
      console.error('❌ 网络错误:', error.message);
    }
  }
}

// 主函数
async function main() {
  console.log('🚀 农场API字段映射测试开始');
  console.log('='.repeat(50));
  
  await testFieldMapping();
  
  console.log('\n' + '='.repeat(50));
  console.log('📋 测试完成');
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(error => {
    console.error('❌ 脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = { testFieldMapping };
