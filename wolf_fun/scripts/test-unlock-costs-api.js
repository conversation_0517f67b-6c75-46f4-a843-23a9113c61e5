#!/usr/bin/env node

/**
 * 测试农场API中的解锁费用
 */

const jwt = require('jsonwebtoken');
const axios = require('axios');
require('dotenv').config();

async function testUnlockCostsAPI() {
  console.log('🧪 测试农场API中的解锁费用\n');

  const baseUrl = 'http://localhost:3456/api';
  const jwtSecret = process.env.JWT_SECRET_Wallet;
  
  if (!jwtSecret) {
    console.error('❌ 缺少 JWT_SECRET_Wallet 环境变量');
    return;
  }

  try {
    // 1. 生成测试JWT token
    console.log('1. 生成测试JWT token...');
    
    const testPayload = {
      walletId: 1,
      walletAddress: 'test_wallet_address',
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (60 * 60) // 1小时后过期
    };
    
    const token = jwt.sign(testPayload, jwtSecret);
    console.log(`✅ Token生成成功`);

    // 2. 测试农场区块API
    console.log('\n2. 测试农场区块API中的解锁费用...');
    
    const farmPlotsResponse = await axios.get(`${baseUrl}/farm/farm-plots`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    console.log(`✅ 农场区块API: ${farmPlotsResponse.status}`);
    
    if (farmPlotsResponse.data.ok) {
      const farmPlots = farmPlotsResponse.data.data.farmPlots;
      console.log(`📊 返回数据: ${farmPlots.length} 个农场区块`);
      
      // 预期的解锁费用（来自 FALLBACK_FARM_PLOT_UNLOCK_COST）
      const expectedUnlockCosts = [
        0, 378250, 1418625, 3405000, 13241250, 7566500, 15322500, 26484000, 
        62426250, 30645750, 55333125, 87399000, 170257500, 78697000, 135072000, 
        204312000, 359437500, 160801250, 268160625, 395385000
      ];

      console.log('\n📊 解锁费用验证:');
      console.log('区块编号 | API返回费用 | 预期费用    | 状态');
      console.log('-'.repeat(50));

      let allCorrect = true;

      for (let i = 0; i < Math.min(farmPlots.length, 10); i++) { // 只显示前10个
        const plot = farmPlots[i];
        const expected = expectedUnlockCosts[plot.plotNumber - 1];
        const actual = parseFloat(plot.unlockCost);
        const isCorrect = Math.abs(actual - expected) < 0.001;
        const status = isCorrect ? '✅' : '❌';
        
        console.log(`${plot.plotNumber.toString().padStart(8)} | ${actual.toString().padStart(11)} | ${expected.toString().padStart(11)} | ${status}`);
        
        if (!isCorrect) {
          allCorrect = false;
        }
      }

      if (farmPlots.length > 10) {
        console.log(`... 还有 ${farmPlots.length - 10} 个区块`);
      }

      console.log('\n🔍 详细检查前5个区块:');
      for (let i = 0; i < Math.min(farmPlots.length, 5); i++) {
        const plot = farmPlots[i];
        const expected = expectedUnlockCosts[plot.plotNumber - 1];
        const actual = parseFloat(plot.unlockCost);
        const isCorrect = Math.abs(actual - expected) < 0.001;
        
        console.log(`\n📦 区块 ${plot.plotNumber}:`);
        console.log(`   是否解锁: ${plot.isUnlocked ? '是' : '否'}`);
        console.log(`   解锁费用: ${actual} (预期: ${expected}) ${isCorrect ? '✅' : '❌'}`);
        console.log(`   等级: ${plot.level}`);
        console.log(`   牛奶产量: ${plot.milkProduction}`);
        console.log(`   升级费用: ${plot.upgradeCost}`);
      }

      console.log('\n📋 解锁费用总结:');
      if (allCorrect) {
        console.log('🎉 所有解锁费用都正确！');
        console.log('✅ 农场API正确使用了统一的解锁费用计算方法');
      } else {
        console.log('❌ 发现不正确的解锁费用');
        console.log('⚠️ 需要检查 FarmPlotCalculator.calculateUnlockCost() 方法');
      }

    } else {
      console.log(`❌ API返回错误: ${farmPlotsResponse.data.message}`);
    }

  } catch (error) {
    if (error.response) {
      console.error(`❌ API错误: ${error.response.status} - ${error.response.data.message || error.response.data}`);
    } else {
      console.error('❌ 网络错误:', error.message);
    }
  }
}

// 主函数
async function main() {
  console.log('🚀 农场API解锁费用测试开始');
  console.log('='.repeat(50));
  
  await testUnlockCostsAPI();
  
  console.log('\n' + '='.repeat(50));
  console.log('📋 测试完成');
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(error => {
    console.error('❌ 脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = { testUnlockCostsAPI };
