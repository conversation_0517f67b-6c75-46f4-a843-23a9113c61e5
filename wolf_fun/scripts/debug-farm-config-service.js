#!/usr/bin/env node

/**
 * 调试农场配置服务脚本
 * 直接测试 FarmConfigService 和模型方法
 */

const path = require('path');
const projectRoot = path.join(__dirname, '..');

// 动态导入模块
async function debugFarmConfigService() {
  try {
    console.log('🔍 调试农场配置服务...\n');

    // 导入必要的模块
    const { sequelize } = require(path.join(projectRoot, 'src/config/db'));
    const { FarmConfig } = require(path.join(projectRoot, 'src/models/FarmConfig'));
    const { FarmConfigVersion } = require(path.join(projectRoot, 'src/models/FarmConfigVersion'));
    const { FarmConfigService } = require(path.join(projectRoot, 'src/services/farmConfigService'));

    console.log('✅ 模块导入成功');

    // 测试数据库连接
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');

    // 1. 直接测试 FarmConfig.getActiveConfigs()
    console.log('\n📊 测试 FarmConfig.getActiveConfigs()...');
    const directConfigs = await FarmConfig.getActiveConfigs();
    console.log(`   结果: ${directConfigs.length} 条配置`);
    
    if (directConfigs.length > 0) {
      console.log(`   样本: 等级${directConfigs[0].grade}, 产量=${directConfigs[0].production}`);
    }

    // 2. 测试 FarmConfigService.getCurrentConfig()
    console.log('\n🔧 测试 FarmConfigService.getCurrentConfig()...');
    const serviceConfigs = await FarmConfigService.getCurrentConfig();
    console.log(`   结果: ${serviceConfigs.length} 条配置`);
    
    if (serviceConfigs.length > 0) {
      console.log(`   样本: 等级${serviceConfigs[0].grade}, 产量=${serviceConfigs[0].production}`);
    }

    // 3. 测试 FarmConfigVersion.getActiveVersion()
    console.log('\n📋 测试 FarmConfigVersion.getActiveVersion()...');
    const activeVersion = await FarmConfigVersion.getActiveVersion();
    console.log(`   结果: ${activeVersion ? activeVersion.version : '无激活版本'}`);
    
    if (activeVersion) {
      console.log(`   版本名称: ${activeVersion.name}`);
      console.log(`   配置数量: ${activeVersion.configCount}`);
    }

    // 4. 检查 Redis 缓存
    console.log('\n💾 检查 Redis 缓存...');
    try {
      const { redis } = require(path.join(projectRoot, 'src/config/redis'));
      const cacheKey = 'farm_config:active';
      const cached = await redis.get(cacheKey);
      
      if (cached) {
        const cachedData = JSON.parse(cached);
        console.log(`   缓存存在: ${cachedData.length} 条配置`);
      } else {
        console.log(`   缓存不存在或为空`);
      }
    } catch (redisError) {
      console.log(`   Redis 错误: ${redisError.message}`);
    }

    // 5. 清除缓存并重新测试
    console.log('\n🗑️ 清除缓存并重新测试...');
    try {
      const { redis } = require(path.join(projectRoot, 'src/config/redis'));
      await redis.del('farm_config:active');
      console.log('   缓存已清除');
      
      const freshConfigs = await FarmConfigService.getCurrentConfig();
      console.log(`   重新获取: ${freshConfigs.length} 条配置`);
      
      if (freshConfigs.length > 0) {
        console.log(`   样本: 等级${freshConfigs[0].grade}, 产量=${freshConfigs[0].production}`);
      }
    } catch (error) {
      console.log(`   清除缓存失败: ${error.message}`);
    }

    console.log('\n✅ 调试完成');

  } catch (error) {
    console.error('❌ 调试失败:', error);
    console.error('错误详情:', error.stack);
  } finally {
    try {
      const { sequelize } = require(path.join(projectRoot, 'src/config/db'));
      await sequelize.close();
    } catch (closeError) {
      console.error('关闭数据库连接失败:', closeError);
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  debugFarmConfigService().catch(error => {
    console.error('❌ 脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = debugFarmConfigService;
