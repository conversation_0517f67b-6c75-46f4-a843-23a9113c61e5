#!/usr/bin/env node

/**
 * 检查数据库表结构脚本
 * 用于诊断表结构问题
 */

const mysql = require('mysql2/promise');
require('dotenv').config();

async function checkDatabaseStructure() {
  const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT) || 3669,
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASS || 'root',
    database: process.env.DB_NAME || 'wolf_fun_db'
  };

  let connection;

  try {
    console.log('🔍 检查数据库表结构...\n');
    console.log('数据库配置:', {
      host: dbConfig.host,
      port: dbConfig.port,
      user: dbConfig.user,
      database: dbConfig.database
    });

    // 创建连接
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功\n');

    // 检查表是否存在
    const [tables] = await connection.execute(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = ?
    `, [dbConfig.database]);

    console.log('📋 数据库中的表:');
    tables.forEach(table => {
      console.log(`  - ${table.TABLE_NAME}`);
    });

    // 检查农场配置相关表
    const farmTables = ['farm_configs', 'farm_config_versions'];
    
    for (const tableName of farmTables) {
      console.log(`\n🔍 检查表: ${tableName}`);
      
      try {
        const [columns] = await connection.execute(`
          SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, EXTRA
          FROM INFORMATION_SCHEMA.COLUMNS 
          WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ?
          ORDER BY ORDINAL_POSITION
        `, [dbConfig.database, tableName]);

        if (columns.length > 0) {
          console.log('  字段结构:');
          columns.forEach(col => {
            console.log(`    ${col.COLUMN_NAME}: ${col.DATA_TYPE} ${col.IS_NULLABLE === 'NO' ? 'NOT NULL' : 'NULL'} ${col.COLUMN_DEFAULT ? `DEFAULT ${col.COLUMN_DEFAULT}` : ''} ${col.EXTRA}`);
          });

          // 检查数据量
          const [countResult] = await connection.execute(`SELECT COUNT(*) as count FROM ${tableName}`);
          console.log(`  数据量: ${countResult[0].count} 条`);

          // 如果是 farm_configs 表，检查激活状态
          if (tableName === 'farm_configs') {
            const [activeResult] = await connection.execute(`SELECT COUNT(*) as count FROM ${tableName} WHERE isActive = 1`);
            console.log(`  激活配置: ${activeResult[0].count} 条`);
          }

          // 如果是 farm_config_versions 表，检查激活版本
          if (tableName === 'farm_config_versions') {
            const [activeVersions] = await connection.execute(`SELECT version, name, isActive FROM ${tableName} ORDER BY createdAt DESC`);
            console.log('  版本列表:');
            activeVersions.forEach(v => {
              console.log(`    ${v.version} (${v.name}) - ${v.isActive ? '激活' : '未激活'}`);
            });
          }
        } else {
          console.log('  ❌ 表不存在');
        }
      } catch (error) {
        console.log(`  ❌ 检查失败: ${error.message}`);
      }
    }

  } catch (error) {
    console.error('❌ 检查失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 主函数
async function main() {
  try {
    await checkDatabaseStructure();
  } catch (error) {
    console.error('❌ 执行失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = checkDatabaseStructure;
