// 简单的集成测试脚本
console.log('🧪 测试流水线配置集成...\n');

// 检查文件是否存在
const fs = require('fs');
const path = require('path');

function checkFileExists(filePath, description) {
  const fullPath = path.join(__dirname, '..', filePath);
  if (fs.existsSync(fullPath)) {
    console.log(`✅ ${description}: ${filePath}`);
    return true;
  } else {
    console.log(`❌ ${description}: ${filePath} (文件不存在)`);
    return false;
  }
}

function checkFileContent(filePath, searchText, description) {
  const fullPath = path.join(__dirname, '..', filePath);
  if (!fs.existsSync(fullPath)) {
    console.log(`❌ ${description}: ${filePath} (文件不存在)`);
    return false;
  }
  
  const content = fs.readFileSync(fullPath, 'utf8');
  if (content.includes(searchText)) {
    console.log(`✅ ${description}: 包含 "${searchText}"`);
    return true;
  } else {
    console.log(`❌ ${description}: 不包含 "${searchText}"`);
    return false;
  }
}

console.log('1️⃣ 检查核心文件是否存在...');
let allFilesExist = true;

allFilesExist &= checkFileExists('src/models/DeliveryLineConfig.ts', 'DeliveryLineConfig 模型');
allFilesExist &= checkFileExists('migrations/20250722000000-create-delivery-line-configs.js', '配置表迁移文件');
allFilesExist &= checkFileExists('migrations/20250722000001-migrate-existing-delivery-lines.js', '数据迁移文件');

console.log('\n2️⃣ 检查模型更新...');
let modelUpdated = true;

modelUpdated &= checkFileContent('src/models/DeliveryLine.ts', 'upgradeWithConfig', 'DeliveryLine 包含新升级方法');
modelUpdated &= checkFileContent('src/models/DeliveryLine.ts', 'initializeWithConfig', 'DeliveryLine 包含新初始化方法');
modelUpdated &= checkFileContent('src/models/DeliveryLine.ts', 'getConfig', 'DeliveryLine 包含配置获取方法');

console.log('\n3️⃣ 检查服务更新...');
let servicesUpdated = true;

servicesUpdated &= checkFileContent('src/services/deliveryLineService.ts', 'DeliveryLineConfig', 'deliveryLineService 导入配置模型');
servicesUpdated &= checkFileContent('src/services/deliveryLineService.ts', 'upgradeWithConfig', 'deliveryLineService 使用新升级方法');

console.log('\n4️⃣ 检查批量服务更新...');
let batchServiceUpdated = true;

batchServiceUpdated &= checkFileContent('src/services/batchResourceUpdateService.ts', 'initializeWithConfig', 'batchResourceUpdateService 使用新初始化方法');

console.log('\n5️⃣ 检查测试重置服务更新...');
let testResetUpdated = true;

testResetUpdated &= checkFileContent('src/services/testResetService.ts', 'DeliveryLineConfig', 'testResetService 导入配置模型');
testResetUpdated &= checkFileContent('src/services/testResetService.ts', 'initializeWithConfig', 'testResetService 使用新初始化方法');

console.log('\n6️⃣ 检查控制器更新...');
let controllerUpdated = true;

controllerUpdated &= checkFileContent('src/controllers/deliveryLineController.ts', 'getAllConfigs', 'deliveryLineController 包含配置管理方法');
controllerUpdated &= checkFileContent('src/controllers/deliveryLineController.ts', 'uploadDeliveryLineConfig', 'deliveryLineController 包含配置上传方法');

console.log('\n7️⃣ 检查路由更新...');
let routesUpdated = true;

routesUpdated &= checkFileContent('src/routes/deliveryLineRoutes.ts', '/configs', 'deliveryLineRoutes 包含配置路由');
routesUpdated &= checkFileContent('src/routes/deliveryLineRoutes.ts', 'multer', 'deliveryLineRoutes 支持文件上传');

console.log('\n📊 集成检查结果:');
console.log(`文件存在: ${allFilesExist ? '✅' : '❌'}`);
console.log(`模型更新: ${modelUpdated ? '✅' : '❌'}`);
console.log(`服务更新: ${servicesUpdated ? '✅' : '❌'}`);
console.log(`批量服务更新: ${batchServiceUpdated ? '✅' : '❌'}`);
console.log(`测试重置服务更新: ${testResetUpdated ? '✅' : '❌'}`);
console.log(`控制器更新: ${controllerUpdated ? '✅' : '❌'}`);
console.log(`路由更新: ${routesUpdated ? '✅' : '❌'}`);

const allChecksPass = allFilesExist && modelUpdated && servicesUpdated && 
                     batchServiceUpdated && testResetUpdated && controllerUpdated && routesUpdated;

console.log('\n🎯 总体结果:');
if (allChecksPass) {
  console.log('🎉 所有检查通过！流水线配置已成功集成到现有逻辑中');
  console.log('\n✅ 完成的集成工作:');
  console.log('- DeliveryLineConfig 模型已创建');
  console.log('- DeliveryLine 模型已更新，支持配置驱动');
  console.log('- 所有服务已更新使用新的配置系统');
  console.log('- API 控制器已添加配置管理功能');
  console.log('- 路由已更新支持配置操作');
  console.log('- 数据迁移脚本已创建');
  
  console.log('\n🚀 系统现在支持:');
  console.log('- 50个等级的精确配置');
  console.log('- 配置驱动的升级系统');
  console.log('- Excel文件配置管理');
  console.log('- 向后兼容的API');
  
  process.exit(0);
} else {
  console.log('❌ 部分检查失败，请检查上述问题');
  process.exit(1);
}
