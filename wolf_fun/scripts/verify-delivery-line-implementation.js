const fs = require('fs');
const path = require('path');

function verifyDeliveryLineImplementation() {
    console.log('🔍 验证流水线系统升级实现...\n');
    
    const results = {
        passed: 0,
        failed: 0,
        warnings: 0,
        details: []
    };
    
    function addResult(type, message, details = '') {
        results[type]++;
        results.details.push({ type, message, details });
        
        const icon = type === 'passed' ? '✅' : type === 'failed' ? '❌' : '⚠️';
        console.log(`${icon} ${message}`);
        if (details) console.log(`   ${details}`);
    }
    
    try {
        // 1. 检查模型文件
        console.log('1️⃣ 检查模型文件...');
        
        const deliveryLineConfigPath = path.join(__dirname, '../src/models/DeliveryLineConfig.ts');
        if (fs.existsSync(deliveryLineConfigPath)) {
            addResult('passed', 'DeliveryLineConfig 模型文件存在');
            
            const configContent = fs.readFileSync(deliveryLineConfigPath, 'utf8');
            if (configContent.includes('getConfigByGrade')) {
                addResult('passed', 'DeliveryLineConfig 包含 getConfigByGrade 方法');
            } else {
                addResult('failed', 'DeliveryLineConfig 缺少 getConfigByGrade 方法');
            }
            
            if (configContent.includes('getAllConfigs')) {
                addResult('passed', 'DeliveryLineConfig 包含 getAllConfigs 方法');
            } else {
                addResult('failed', 'DeliveryLineConfig 缺少 getAllConfigs 方法');
            }
        } else {
            addResult('failed', 'DeliveryLineConfig 模型文件不存在');
        }
        
        const deliveryLinePath = path.join(__dirname, '../src/models/DeliveryLine.ts');
        if (fs.existsSync(deliveryLinePath)) {
            addResult('passed', 'DeliveryLine 模型文件存在');
            
            const lineContent = fs.readFileSync(deliveryLinePath, 'utf8');
            if (lineContent.includes('upgradeWithConfig')) {
                addResult('passed', 'DeliveryLine 包含 upgradeWithConfig 方法');
            } else {
                addResult('failed', 'DeliveryLine 缺少 upgradeWithConfig 方法');
            }
            
            if (lineContent.includes('initializeWithConfig')) {
                addResult('passed', 'DeliveryLine 包含 initializeWithConfig 方法');
            } else {
                addResult('failed', 'DeliveryLine 缺少 initializeWithConfig 方法');
            }
            
            if (lineContent.includes('getConfig')) {
                addResult('passed', 'DeliveryLine 包含 getConfig 方法');
            } else {
                addResult('failed', 'DeliveryLine 缺少 getConfig 方法');
            }
            
            if (lineContent.includes('canUpgrade')) {
                addResult('passed', 'DeliveryLine 包含 canUpgrade 方法');
            } else {
                addResult('failed', 'DeliveryLine 缺少 canUpgrade 方法');
            }
        } else {
            addResult('failed', 'DeliveryLine 模型文件不存在');
        }
        
        // 2. 检查迁移文件
        console.log('\n2️⃣ 检查迁移文件...');
        
        const configMigrationPath = path.join(__dirname, '../migrations/20250722000000-create-delivery-line-configs.js');
        if (fs.existsSync(configMigrationPath)) {
            addResult('passed', '配置表创建迁移文件存在');
            
            const migrationContent = fs.readFileSync(configMigrationPath, 'utf8');
            if (migrationContent.includes('delivery_line_configs')) {
                addResult('passed', '迁移文件包含配置表创建');
            } else {
                addResult('failed', '迁移文件不包含配置表创建');
            }
            
            // 检查是否包含50个等级的数据
            const gradeMatches = migrationContent.match(/grade:\s*\d+/g);
            if (gradeMatches && gradeMatches.length >= 50) {
                addResult('passed', '迁移文件包含50个等级的配置数据');
            } else {
                addResult('failed', `迁移文件配置数据不完整，找到 ${gradeMatches ? gradeMatches.length : 0} 个等级`);
            }
        } else {
            addResult('failed', '配置表创建迁移文件不存在');
        }
        
        const dataMigrationPath = path.join(__dirname, '../migrations/20250722000001-migrate-existing-delivery-lines.js');
        if (fs.existsSync(dataMigrationPath)) {
            addResult('passed', '数据迁移文件存在');
        } else {
            addResult('failed', '数据迁移文件不存在');
        }
        
        // 3. 检查服务文件
        console.log('\n3️⃣ 检查服务文件...');
        
        const servicePath = path.join(__dirname, '../src/services/deliveryLineService.ts');
        if (fs.existsSync(servicePath)) {
            addResult('passed', 'deliveryLineService 文件存在');
            
            const serviceContent = fs.readFileSync(servicePath, 'utf8');
            if (serviceContent.includes('DeliveryLineConfig')) {
                addResult('passed', 'deliveryLineService 导入了 DeliveryLineConfig');
            } else {
                addResult('failed', 'deliveryLineService 未导入 DeliveryLineConfig');
            }
            
            if (serviceContent.includes('initializeWithConfig')) {
                addResult('passed', 'deliveryLineService 使用了 initializeWithConfig');
            } else {
                addResult('failed', 'deliveryLineService 未使用 initializeWithConfig');
            }
            
            if (serviceContent.includes('upgradeWithConfig')) {
                addResult('passed', 'deliveryLineService 使用了 upgradeWithConfig');
            } else {
                addResult('failed', 'deliveryLineService 未使用 upgradeWithConfig');
            }
        } else {
            addResult('failed', 'deliveryLineService 文件不存在');
        }
        
        // 4. 检查控制器文件
        console.log('\n4️⃣ 检查控制器文件...');
        
        const controllerPath = path.join(__dirname, '../src/controllers/deliveryLineController.ts');
        if (fs.existsSync(controllerPath)) {
            addResult('passed', 'deliveryLineController 文件存在');
            
            const controllerContent = fs.readFileSync(controllerPath, 'utf8');
            if (controllerContent.includes('getAllConfigs')) {
                addResult('passed', 'deliveryLineController 包含 getAllConfigs 方法');
            } else {
                addResult('failed', 'deliveryLineController 缺少 getAllConfigs 方法');
            }
            
            if (controllerContent.includes('uploadDeliveryLineConfig')) {
                addResult('passed', 'deliveryLineController 包含 uploadDeliveryLineConfig 方法');
            } else {
                addResult('failed', 'deliveryLineController 缺少 uploadDeliveryLineConfig 方法');
            }
            
            if (controllerContent.includes('XLSX')) {
                addResult('passed', 'deliveryLineController 支持Excel文件处理');
            } else {
                addResult('failed', 'deliveryLineController 不支持Excel文件处理');
            }
        } else {
            addResult('failed', 'deliveryLineController 文件不存在');
        }
        
        // 5. 检查路由文件
        console.log('\n5️⃣ 检查路由文件...');
        
        const routesPath = path.join(__dirname, '../src/routes/deliveryLineRoutes.ts');
        if (fs.existsSync(routesPath)) {
            addResult('passed', 'deliveryLineRoutes 文件存在');
            
            const routesContent = fs.readFileSync(routesPath, 'utf8');
            if (routesContent.includes('/configs')) {
                addResult('passed', 'deliveryLineRoutes 包含配置查询路由');
            } else {
                addResult('failed', 'deliveryLineRoutes 缺少配置查询路由');
            }
            
            if (routesContent.includes('/upload-config')) {
                addResult('passed', 'deliveryLineRoutes 包含配置上传路由');
            } else {
                addResult('failed', 'deliveryLineRoutes 缺少配置上传路由');
            }
            
            if (routesContent.includes('multer')) {
                addResult('passed', 'deliveryLineRoutes 配置了文件上传');
            } else {
                addResult('failed', 'deliveryLineRoutes 未配置文件上传');
            }
        } else {
            addResult('failed', 'deliveryLineRoutes 文件不存在');
        }
        
        // 6. 检查其他服务的更新
        console.log('\n6️⃣ 检查其他服务的更新...');
        
        const batchServicePath = path.join(__dirname, '../src/services/batchResourceUpdateService.ts');
        if (fs.existsSync(batchServicePath)) {
            const batchContent = fs.readFileSync(batchServicePath, 'utf8');
            if (batchContent.includes('initializeWithConfig')) {
                addResult('passed', 'batchResourceUpdateService 已更新使用新初始化方法');
            } else {
                addResult('warnings', 'batchResourceUpdateService 可能未更新使用新初始化方法');
            }
        }
        
        // 7. 检查uploads目录
        console.log('\n7️⃣ 检查uploads目录...');
        
        const uploadsPath = path.join(__dirname, '../uploads');
        if (fs.existsSync(uploadsPath)) {
            addResult('passed', 'uploads 目录存在');
        } else {
            addResult('warnings', 'uploads 目录不存在，可能影响文件上传功能');
        }
        
        // 8. 检查测试脚本
        console.log('\n8️⃣ 检查测试脚本...');
        
        const testScripts = [
            'test-delivery-line-configs.js',
            'test-delivery-line-api.js',
            'verify-delivery-line-implementation.js'
        ];
        
        testScripts.forEach(script => {
            const scriptPath = path.join(__dirname, script);
            if (fs.existsSync(scriptPath)) {
                addResult('passed', `测试脚本 ${script} 存在`);
            } else {
                addResult('warnings', `测试脚本 ${script} 不存在`);
            }
        });
        
        // 总结
        console.log('\n📊 验证结果总结:');
        console.log(`✅ 通过: ${results.passed}`);
        console.log(`❌ 失败: ${results.failed}`);
        console.log(`⚠️ 警告: ${results.warnings}`);
        
        if (results.failed === 0) {
            console.log('\n🎉 流水线系统升级实现验证通过！');
            console.log('\n📋 下一步操作:');
            console.log('1. 启动服务器: npm run dev');
            console.log('2. 运行API测试: node scripts/test-delivery-line-api.js');
            console.log('3. 测试Excel配置上传功能');
            console.log('4. 验证现有用户的流水线数据');
        } else {
            console.log('\n⚠️ 发现问题，请修复后重新验证');
        }
        
        return results.failed === 0;
        
    } catch (error) {
        console.error('\n❌ 验证过程中发生错误:', error.message);
        return false;
    }
}

// 执行验证
if (require.main === module) {
    const success = verifyDeliveryLineImplementation();
    process.exit(success ? 0 : 1);
}

module.exports = { verifyDeliveryLineImplementation };
