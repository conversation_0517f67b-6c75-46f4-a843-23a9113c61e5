const request = require('supertest');
const { expect } = require('chai');
const { sequelize } = require('../src/config/db');
const { FarmConfig } = require('../src/models/FarmConfig');
const { FarmConfigVersion } = require('../src/models/FarmConfigVersion');
const { FarmConfigService } = require('../src/services/farmConfigService');
const app = require('../src/app').default;

describe('农场配置系统测试', function() {
  this.timeout(30000); // 设置超时时间为30秒

  before(async function() {
    // 确保数据库连接
    await sequelize.authenticate();
    
    // 同步数据库模型
    await sequelize.sync({ force: false });
    
    console.log('测试环境数据库连接成功');
  });

  after(async function() {
    // 清理测试数据
    await FarmConfig.destroy({ where: { version: { $like: 'test_%' } } });
    await FarmConfigVersion.destroy({ where: { version: { $like: 'test_%' } } });
    
    // 关闭数据库连接
    await sequelize.close();
  });

  describe('FarmConfig 模型测试', function() {
    const testVersion = 'test_version_1';
    const testConfigs = [
      { grade: 0, production: 0, cow: 0, speed: 100, milk: 0, cost: 1000, offline: 0 },
      { grade: 1, production: 100, cow: 1, speed: 105, milk: 1.5, cost: 2000, offline: 0.75 },
      { grade: 2, production: 200, cow: 2, speed: 110, milk: 3.0, cost: 4000, offline: 1.5 }
    ];

    beforeEach(async function() {
      // 清理测试数据
      await FarmConfig.destroy({ where: { version: testVersion } });
      await FarmConfigVersion.destroy({ where: { version: testVersion } });
    });

    it('应该能够批量创建配置数据', async function() {
      const createdConfigs = await FarmConfig.bulkCreateConfigs(
        testConfigs,
        testVersion,
        'test_user'
      );

      expect(createdConfigs).to.have.length(3);
      expect(createdConfigs[0].version).to.equal(testVersion);
      expect(createdConfigs[0].createdBy).to.equal('test_user');
    });

    it('应该能够验证配置数据', function() {
      const validation = FarmConfig.validateConfigData(testConfigs);
      expect(validation.isValid).to.be.true;
      expect(validation.errors).to.be.empty;
    });

    it('应该能够检测无效的配置数据', function() {
      const invalidConfigs = [
        { grade: -1, production: 0, cow: 0, speed: 100, milk: 0, cost: 1000, offline: 0 },
        { grade: 1, production: -100, cow: 1, speed: 105, milk: 1.5, cost: 2000, offline: 0.75 }
      ];

      const validation = FarmConfig.validateConfigData(invalidConfigs);
      expect(validation.isValid).to.be.false;
      expect(validation.errors).to.not.be.empty;
    });

    it('应该能够激活指定版本', async function() {
      // 创建测试配置
      await FarmConfig.bulkCreateConfigs(testConfigs, testVersion, 'test_user');
      
      // 激活版本
      const success = await FarmConfig.activateVersion(testVersion);
      expect(success).to.be.true;

      // 验证激活状态
      const activeConfigs = await FarmConfig.getActiveConfigs();
      expect(activeConfigs).to.have.length(3);
      expect(activeConfigs[0].version).to.equal(testVersion);
    });

    it('应该能够根据等级获取配置', async function() {
      // 创建并激活测试配置
      await FarmConfig.bulkCreateConfigs(testConfigs, testVersion, 'test_user');
      await FarmConfig.activateVersion(testVersion);

      // 获取指定等级配置
      const config = await FarmConfig.getActiveConfigByGrade(1);
      expect(config).to.not.be.null;
      expect(config.grade).to.equal(1);
      expect(config.cow).to.equal(1);
    });
  });

  describe('FarmConfigVersion 模型测试', function() {
    const testVersion = 'test_version_2';

    beforeEach(async function() {
      await FarmConfigVersion.destroy({ where: { version: testVersion } });
    });

    it('应该能够创建版本记录', async function() {
      const version = await FarmConfigVersion.createVersion(
        testVersion,
        '测试版本',
        51,
        '这是一个测试版本',
        'test_user'
      );

      expect(version.version).to.equal(testVersion);
      expect(version.name).to.equal('测试版本');
      expect(version.configCount).to.equal(51);
    });

    it('应该能够激活版本', async function() {
      // 创建版本记录
      await FarmConfigVersion.createVersion(testVersion, '测试版本', 51, '', 'test_user');

      // 激活版本
      const success = await FarmConfigVersion.activateVersion(testVersion);
      expect(success).to.be.true;

      // 验证激活状态
      const activeVersion = await FarmConfigVersion.getActiveVersion();
      expect(activeVersion.version).to.equal(testVersion);
    });

    it('应该能够获取版本统计', async function() {
      // 创建测试版本
      await FarmConfigVersion.createVersion(testVersion, '测试版本', 51, '', 'test_user');

      const stats = await FarmConfigVersion.getVersionStats();
      expect(stats).to.have.property('totalVersions');
      expect(stats.totalVersions).to.be.at.least(1);
    });
  });

  describe('FarmConfigService 服务测试', function() {
    const testVersion = 'test_version_3';
    const testConfigs = [
      { grade: 0, production: 0, cow: 0, speed: 100, milk: 0, cost: 1000, offline: 0 },
      { grade: 1, production: 100, cow: 1, speed: 105, milk: 1.5, cost: 2000, offline: 0.75 }
    ];

    beforeEach(async function() {
      await FarmConfig.destroy({ where: { version: testVersion } });
      await FarmConfigVersion.destroy({ where: { version: testVersion } });
    });

    it('应该能够获取当前配置', async function() {
      // 创建并激活测试配置
      await FarmConfig.bulkCreateConfigs(testConfigs, testVersion, 'test_user');
      await FarmConfigVersion.createVersion(testVersion, '测试版本', 2, '', 'test_user');
      await FarmConfig.activateVersion(testVersion);
      await FarmConfigVersion.activateVersion(testVersion);

      const configs = await FarmConfigService.getCurrentConfig();
      expect(configs).to.have.length(2);
      expect(configs[0].version).to.equal(testVersion);
    });

    it('应该能够根据等级获取配置', async function() {
      // 创建并激活测试配置
      await FarmConfig.bulkCreateConfigs(testConfigs, testVersion, 'test_user');
      await FarmConfig.activateVersion(testVersion);

      const config = await FarmConfigService.getConfigByGrade(1);
      expect(config).to.not.be.null;
      expect(config.grade).to.equal(1);
    });

    it('应该能够获取版本统计', async function() {
      const stats = await FarmConfigService.getVersionStats();
      expect(stats).to.have.property('totalVersions');
      expect(stats).to.have.property('totalConfigs');
    });
  });

  describe('API 接口测试', function() {
    describe('公开接口', function() {
      it('GET /api/admin/farm-config/current - 应该返回当前配置', async function() {
        const response = await request(app)
          .get('/api/admin/farm-config/current')
          .expect(200);

        expect(response.body).to.have.property('ok', true);
        expect(response.body.data).to.have.property('configs');
      });

      it('GET /api/admin/farm-config/stats - 应该返回统计信息', async function() {
        const response = await request(app)
          .get('/api/admin/farm-config/stats')
          .expect(200);

        expect(response.body).to.have.property('ok', true);
        expect(response.body.data).to.have.property('totalVersions');
      });
    });

    describe('管理员接口', function() {
      // 注意：这些测试需要管理员权限，在实际测试中需要模拟认证

      it('GET /api/admin/farm-config/versions - 应该返回版本列表', async function() {
        // 在开发环境下测试
        if (process.env.NODE_ENV === 'development') {
          const response = await request(app)
            .get('/api/admin/farm-config/versions')
            .expect(200);

          expect(response.body).to.have.property('ok', true);
          expect(response.body.data).to.have.property('versions');
        }
      });

      it('POST /api/admin/farm-config/warmup-cache - 应该预热缓存', async function() {
        if (process.env.NODE_ENV === 'development') {
          const response = await request(app)
            .post('/api/admin/farm-config/warmup-cache')
            .expect(200);

          expect(response.body).to.have.property('ok', true);
        }
      });
    });
  });

  describe('配置函数兼容性测试', function() {
    const { 
      getFarmPlotBarnCount,
      getFarmPlotProductionSpeed,
      getFarmPlotMilkProduction,
      getFarmPlotUpgradeCost,
      getFarmPlotUnlockCost
    } = require('../src/config/farmPlotConfig');

    it('应该能够获取牛舍数量', async function() {
      const barnCount = await getFarmPlotBarnCount(1);
      expect(barnCount).to.be.a('number');
      expect(barnCount).to.be.at.least(0);
    });

    it('应该能够获取生产速度', async function() {
      const speed = await getFarmPlotProductionSpeed(1);
      expect(speed).to.be.a('number');
      expect(speed).to.be.greaterThan(0);
    });

    it('应该能够获取解锁费用', function() {
      const cost = getFarmPlotUnlockCost(1);
      expect(cost).to.be.a('number');
      expect(cost).to.be.at.least(0);
    });

    it('应该能够获取牛奶产量', async function() {
      const production = await getFarmPlotMilkProduction(1, 1);
      expect(production).to.be.a('number');
      expect(production).to.be.at.least(0);
    });

    it('应该能够获取升级费用', async function() {
      const cost = await getFarmPlotUpgradeCost(1, 1);
      expect(cost).to.be.a('number');
      expect(cost).to.be.at.least(0);
    });

    it('应该支持新的等级范围 (0-50)', async function() {
      // 测试新的等级范围
      const barnCount = await getFarmPlotBarnCount(25);
      expect(barnCount).to.be.a('number');

      const speed = await getFarmPlotProductionSpeed(30);
      expect(speed).to.be.a('number');
    });

    it('应该在超出范围时抛出错误', async function() {
      try {
        await getFarmPlotBarnCount(100);
        expect.fail('应该抛出错误');
      } catch (error) {
        expect(error.message).to.include('Invalid level');
      }
    });
  });
});
