// 测试batch-update-resources API中VIP和加速道具效果的正确应用
// 验证VIP会员和Speed Boost道具对资源计算的影响

console.log('=== Batch Update Resources API - VIP和加速道具效果测试 ===\n');

// 模拟用户数据
const testUser = {
  walletId: 1,
  gem: 1000.000,
  lastActiveTime: new Date(Date.now() - 30 * 1000) // 30秒前
};

// 基础农场区块数据
const baseFarmPlot = {
  id: 1,
  walletId: 1,
  plotNumber: 1,
  level: 2,
  barnCount: 2,
  milkProduction: 1.500,      // 每周期单个谷仓产量
  productionSpeed: 4.762,     // 基础生产速度（秒）
  upgradeCost: 300,
  unlockCost: 0,
  isUnlocked: true,
  accumulatedMilk: 0
};

// 基础出货线数据
const baseDeliveryLine = {
  id: 1,
  walletId: 1,
  level: 2,
  deliverySpeed: 4.950,       // 基础出货速度（秒）
  blockUnit: 10.000,          // 方块容量
  blockPrice: 10.000,         // 基础方块价格（GEM）
  upgradeCost: 1000,
  pendingMilk: 15.000
};

// VIP效果
const vipEffects = {
  isVip: true,
  deliverySpeedMultiplier: 1.3,  // 30% 出货线速度加成
  blockPriceMultiplier: 1.2,     // 20% 出货线价格加成
  productionSpeedMultiplier: 1.3  // 30% 牧场区生产速度加成
};

// 非VIP效果
const nonVipEffects = {
  isVip: false,
  deliverySpeedMultiplier: 1,
  blockPriceMultiplier: 1,
  productionSpeedMultiplier: 1
};

// Speed Boost道具效果
const speedBoostEffects = {
  none: { speedMultiplier: 1.0 },     // 无道具
  x2: { speedMultiplier: 3.0 },       // +200% 速度加成 (实际是3倍)
  x4: { speedMultiplier: 5.0 }        // +400% 速度加成 (实际是5倍)
};

// 测试函数：计算加成后的资源产出（协调计算）
function calculateBoostedResourceProduction(farmPlot, deliveryLine, timeElapsedSeconds, vipEffects, boosterEffects, initialPendingMilk = 15) {
  // 计算加成效果
  const productionSpeedMultiplier = vipEffects.productionSpeedMultiplier || 1;
  const deliverySpeedMultiplier = (vipEffects.deliverySpeedMultiplier || 1) * (boosterEffects.speedMultiplier || 1);
  const blockPriceMultiplier = vipEffects.blockPriceMultiplier || 1;

  // 应用加成后的实际速度
  const actualProductionSpeed = farmPlot.productionSpeed / productionSpeedMultiplier;
  const actualDeliverySpeed = deliveryLine.deliverySpeed / deliverySpeedMultiplier;
  const actualBlockPrice = deliveryLine.blockPrice * blockPriceMultiplier;

  // 协调计算逻辑 - 模拟事件队列
  let currentPendingMilk = initialPendingMilk;
  let totalFarmProduced = 0;
  let totalDeliveryConsumed = 0;
  let totalGemProduced = 0;

  // 创建事件队列
  const events = [];

  // 添加农场生产事件
  let nextProductionTime = actualProductionSpeed;
  while (nextProductionTime <= timeElapsedSeconds) {
    events.push({
      time: nextProductionTime,
      type: 'farm',
      amount: farmPlot.milkProduction * farmPlot.barnCount  // 考虑谷仓数量
    });
    nextProductionTime += actualProductionSpeed;
  }

  // 添加出货线处理事件
  let nextDeliveryTime = actualDeliverySpeed;
  while (nextDeliveryTime <= timeElapsedSeconds) {
    events.push({
      time: nextDeliveryTime,
      type: 'delivery',
      amount: deliveryLine.blockUnit,
      gemPrice: actualBlockPrice
    });
    nextDeliveryTime += actualDeliverySpeed;
  }

  // 按时间排序事件
  events.sort((a, b) => a.time - b.time);

  // 处理事件队列
  for (const event of events) {
    if (event.type === 'farm') {
      // 农场生产事件
      currentPendingMilk += event.amount;
      totalFarmProduced += event.amount;
    } else if (event.type === 'delivery') {
      // 出货线处理事件
      const requiredMilk = event.amount;

      if (currentPendingMilk >= requiredMilk) {
        // 有足够牛奶，可以处理
        currentPendingMilk -= requiredMilk;
        totalDeliveryConsumed += requiredMilk;
        totalGemProduced += event.gemPrice;
      }
      // 如果牛奶不够，出货线等待（跳过这次处理）
    }
  }

  return {
    farmMilkProduced: Number(totalFarmProduced.toFixed(3)),
    deliveryMilkConsumed: Number(totalDeliveryConsumed.toFixed(3)),
    gemProduced: Number(totalGemProduced.toFixed(3)),
    finalPendingMilk: Number(currentPendingMilk.toFixed(3)),
    actualProductionSpeed: Number(actualProductionSpeed.toFixed(3)),
    actualDeliverySpeed: Number(actualDeliverySpeed.toFixed(3)),
    actualBlockPrice: Number(actualBlockPrice.toFixed(3)),
    farmEvents: events.filter(e => e.type === 'farm').length,
    deliveryEvents: events.filter(e => e.type === 'delivery').length,
    successfulDeliveries: Math.floor(totalDeliveryConsumed / deliveryLine.blockUnit)
  };
}

// 测试不同加成组合
function testBoostCombinations() {
  console.log('🧪 测试不同VIP和加速道具组合对资源计算的影响\n');
  
  const timeElapsedSeconds = 30; // 30秒测试时间
  
  const testCases = [
    {
      name: '无加成',
      vip: nonVipEffects,
      booster: speedBoostEffects.none
    },
    {
      name: '仅VIP会员',
      vip: vipEffects,
      booster: speedBoostEffects.none
    },
    {
      name: 'VIP + Speed Boost x2',
      vip: vipEffects,
      booster: speedBoostEffects.x2
    },
    {
      name: 'VIP + Speed Boost x4',
      vip: vipEffects,
      booster: speedBoostEffects.x4
    },
    {
      name: '仅Speed Boost x2',
      vip: nonVipEffects,
      booster: speedBoostEffects.x2
    },
    {
      name: '仅Speed Boost x4',
      vip: nonVipEffects,
      booster: speedBoostEffects.x4
    }
  ];

  testCases.forEach((testCase, index) => {
    console.log(`${index + 1}. ${testCase.name}`);
    console.log('─'.repeat(40));
    
    const result = calculateBoostedResourceProduction(
      baseFarmPlot,
      baseDeliveryLine,
      timeElapsedSeconds,
      testCase.vip,
      testCase.booster
    );
    
    console.log(`农场生产:`);
    console.log(`  实际生产速度: ${result.actualProductionSpeed}秒/周期`);
    console.log(`  农场生产事件: ${result.farmEvents}次`);
    console.log(`  牛奶产量: ${result.farmMilkProduced}`);

    console.log(`出货线处理:`);
    console.log(`  实际出货速度: ${result.actualDeliverySpeed}秒/周期`);
    console.log(`  实际方块价格: ${result.actualBlockPrice} GEM`);
    console.log(`  出货线处理事件: ${result.deliveryEvents}次`);
    console.log(`  成功处理次数: ${result.successfulDeliveries}次`);
    console.log(`  牛奶消耗: ${result.deliveryMilkConsumed}`);
    console.log(`  GEM产出: ${result.gemProduced}`);
    console.log(`  最终剩余牛奶: ${result.finalPendingMilk}`);
    
    console.log('');
  });
}

// 测试API请求验证逻辑
function testApiRequestValidation() {
  console.log('📋 测试API请求验证逻辑\n');
  
  const timeElapsedSeconds = 30;
  
  // 计算VIP + Speed Boost x2的理论值
  const theoreticalResult = calculateBoostedResourceProduction(
    baseFarmPlot,
    baseDeliveryLine,
    timeElapsedSeconds,
    vipEffects,
    speedBoostEffects.x2
  );
  
  console.log('理论计算值 (VIP + Speed Boost x2):');
  console.log(`  农场牛奶产量: ${theoreticalResult.farmMilkProduced}`);
  console.log(`  出货线牛奶消耗: ${theoreticalResult.deliveryMilkConsumed}`);
  console.log(`  GEM产出: ${theoreticalResult.gemProduced}`);
  console.log('');
  
  // 测试1.5倍验证范围
  const gemLimit = theoreticalResult.gemProduced * 1.5;
  const milkProduceLimit = theoreticalResult.farmMilkProduced * 1.5;
  const milkConsumeLimit = theoreticalResult.deliveryMilkConsumed * 1.5;
  
  console.log('1.5倍验证范围:');
  console.log(`  GEM请求上限: ${gemLimit.toFixed(3)}`);
  console.log(`  牛奶生产上限: ${milkProduceLimit.toFixed(3)}`);
  console.log(`  牛奶消耗上限: ${milkConsumeLimit.toFixed(3)}`);
  console.log('');
  
  // 测试不同的前端请求
  const testRequests = [
    {
      name: '合理请求 (理论值)',
      gemRequest: theoreticalResult.gemProduced,
      milkOperations: {
        produce: theoreticalResult.farmMilkProduced,
        consume: theoreticalResult.deliveryMilkConsumed
      }
    },
    {
      name: '边界请求 (1.5倍上限)',
      gemRequest: gemLimit,
      milkOperations: {
        produce: milkProduceLimit,
        consume: milkConsumeLimit
      }
    },
    {
      name: '超限请求 (2倍)',
      gemRequest: theoreticalResult.gemProduced * 2,
      milkOperations: {
        produce: theoreticalResult.farmMilkProduced * 2,
        consume: theoreticalResult.deliveryMilkConsumed * 2
      }
    }
  ];
  
  testRequests.forEach((request, index) => {
    console.log(`${index + 1}. ${request.name}`);
    console.log('─'.repeat(30));
    
    const gemValid = request.gemRequest <= gemLimit;
    const milkProduceValid = request.milkOperations.produce <= milkProduceLimit;
    const milkConsumeValid = request.milkOperations.consume <= milkConsumeLimit;
    
    console.log(`  GEM请求: ${request.gemRequest.toFixed(3)} - ${gemValid ? '✅ 有效' : '❌ 超限'}`);
    console.log(`  牛奶生产: ${request.milkOperations.produce.toFixed(3)} - ${milkProduceValid ? '✅ 有效' : '❌ 超限'}`);
    console.log(`  牛奶消耗: ${request.milkOperations.consume.toFixed(3)} - ${milkConsumeValid ? '✅ 有效' : '❌ 超限'}`);
    
    if (!gemValid || !milkProduceValid || !milkConsumeValid) {
      console.log(`  预期结果: 使用系统计算值`);
    } else {
      console.log(`  预期结果: 使用前端请求值`);
    }
    
    console.log('');
  });
}

// 运行测试
testBoostCombinations();
testApiRequestValidation();

// 测试farmMilkPerCycle计算
function testFarmMilkPerCycleCalculation() {
  console.log('🔧 测试farmMilkPerCycle计算（考虑谷仓数量）\n');

  const testCases = [
    {
      name: '单个农场区块（2个谷仓）',
      farmPlots: [baseFarmPlot],
      expectedFarmMilkPerCycle: 1.5 * 2  // milkProduction * barnCount
    },
    {
      name: '两个农场区块',
      farmPlots: [
        baseFarmPlot,
        {
          ...baseFarmPlot,
          id: 2,
          plotNumber: 2,
          level: 1,
          barnCount: 1,
          milkProduction: 1.0
        }
      ],
      expectedFarmMilkPerCycle: (1.5 * 2) + (1.0 * 1)  // 第一个区块 + 第二个区块
    }
  ];

  testCases.forEach((testCase, index) => {
    console.log(`${index + 1}. ${testCase.name}`);
    console.log('─'.repeat(30));

    let totalFarmMilkPerCycle = 0;
    testCase.farmPlots.forEach(plot => {
      const plotProduction = plot.milkProduction * plot.barnCount;
      totalFarmMilkPerCycle += plotProduction;
      console.log(`  农场区块${plot.plotNumber}: ${plot.milkProduction} × ${plot.barnCount} = ${plotProduction}`);
    });

    console.log(`  总计farmMilkPerCycle: ${totalFarmMilkPerCycle}`);
    console.log(`  预期值: ${testCase.expectedFarmMilkPerCycle}`);
    console.log(`  结果: ${totalFarmMilkPerCycle === testCase.expectedFarmMilkPerCycle ? '✅ 正确' : '❌ 错误'}`);
    console.log('');
  });
}

console.log('✅ 测试完成！');
console.log('\n📝 测试总结:');
console.log('1. VIP会员提供30%农场生产速度、30%出货速度、20%价格加成');
console.log('2. Speed Boost x2提供200%出货速度加成 (实际3倍速度)');
console.log('3. Speed Boost x4提供400%出货速度加成 (实际5倍速度)');
console.log('4. 加成效果可以叠加：VIP + Speed Boost');
console.log('5. API验证使用1.5倍容错范围，超限请求使用系统计算值');
console.log('6. farmMilkPerCycle正确考虑谷仓数量：milkProduction × barnCount');

// 运行farmMilkPerCycle测试
testFarmMilkPerCycleCalculation();
