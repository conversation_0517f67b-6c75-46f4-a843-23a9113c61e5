# Dapp Portal 支付集成说明

## 概述

本项目已集成 Dapp Portal Payment Provider，支持处理支付回调和状态管理。

## 环境变量配置

在 `.env` 文件中添加以下配置：

```env
# Dapp Portal Payment Provider
DAPP_PORTAL_CLIENT_ID=your_client_id_here
DAPP_PORTAL_CLIENT_SECRET=your_client_secret_here
```

## API 接口

### Webhook 回调接口

1. **Lock 回调** - `POST /api/dapp-portal-payment/lock`
   - 当支付流程开始前调用，用于锁定商品
   - 会验证支付信息状态

2. **Unlock 回调** - `POST /api/dapp-portal-payment/unlock`
   - 当支付被系统取消时调用，用于释放锁定的商品
   - 会更新 IapPurchase 状态为 failed

3. **状态变更回调** - `POST /api/dapp-portal-payment/status`
   - 支付状态变更时调用
   - 会核查实际支付状态
   - 当状态为 CONFIRMED 时自动调用 finalize API

## 功能特性

### 状态核查
- 每次回调都会调用 Dapp Portal API 核查实际支付状态
- 如果回调状态与实际状态不一致，会使用实际状态

### 自动 Finalize
- 当支付状态为 CONFIRMED 时，自动调用 finalize API
- 确保支付能够正常结算

### 状态映射
- CONFIRMED/FINALIZED → completed
- REFUNDED → refunded  
- CONFIRM_FAILED/CANCELED/CHARGEBACK → failed
- 其他状态 → pending

## 使用方法

在创建 Dapp Portal 支付时，配置以下回调 URL：

```javascript
{
  "lockUrl": "https://your-domain.com/api/dapp-portal-payment/lock",
  "unlockUrl": "https://your-domain.com/api/dapp-portal-payment/unlock", 
  "paymentStatusChangeCallbackUrl": "https://your-domain.com/api/dapp-portal-payment/status"
}
```

## 日志记录

所有回调处理都有详细的日志记录，便于调试和监控：

- `[DappPortal][LOCK]` - Lock 事件日志
- `[DappPortal][UNLOCK]` - Unlock 事件日志  
- `[DappPortal][STATUS]` - 状态变更事件日志

## 错误处理

- API 调用失败不会影响回调响应
- 数据库操作失败会记录错误但不影响回调
- 始终返回 200 状态码确保 Dapp Portal 不会重试 