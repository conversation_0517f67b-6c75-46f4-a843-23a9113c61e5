/**
 * 测试重置游戏状态 API 的简单测试脚本
 * 使用方法：node test_reset_api.js
 * 
 * 注意：此脚本仅用于开发环境测试
 */

const axios = require('axios');

// 配置
const BASE_URL = 'http://localhost:3000';
const TEST_TOKEN = 'your_test_jwt_token_here'; // 需要替换为有效的JWT token

// API 客户端
const apiClient = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Authorization': `Bearer ${TEST_TOKEN}`,
    'Content-Type': 'application/json',
    'Accept-Language': 'zh'
  }
});

/**
 * 测试健康检查接口
 */
async function testHealthCheck() {
  console.log('\n=== 测试健康检查接口 ===');
  try {
    const response = await apiClient.get('/api/test/health');
    console.log('✅ 健康检查成功:', response.data);
    return true;
  } catch (error) {
    console.error('❌ 健康检查失败:', error.response?.data || error.message);
    return false;
  }
}

/**
 * 测试获取重置安全检查信息
 */
async function testGetResetSafetyInfo() {
  console.log('\n=== 测试获取重置安全检查信息 ===');
  try {
    const response = await apiClient.get('/api/test/reset-safety-info');
    console.log('✅ 获取安全检查信息成功:', JSON.stringify(response.data, null, 2));
    return response.data.data;
  } catch (error) {
    console.error('❌ 获取安全检查信息失败:', error.response?.data || error.message);
    return null;
  }
}

/**
 * 测试重置游戏状态
 */
async function testResetGameState() {
  console.log('\n=== 测试重置游戏状态 ===');
  try {
    const response = await apiClient.post('/api/test/reset-game-state', {});
    console.log('✅ 重置游戏状态成功');
    console.log(`   重置时间: ${response.data.data.resetTimestamp}`);
    console.log(`   响应消息: ${response.data.message}`);
    return response.data.data;
  } catch (error) {
    console.error('❌ 重置游戏状态失败:', error.response?.data || error.message);
    return null;
  }
}

// 移除了带确认头的测试函数，因为不再需要确认头

/**
 * 测试速率限制
 */
async function testRateLimit() {
  console.log('\n=== 测试速率限制 ===');
  const requests = [];

  // 快速发送多个重置请求
  for (let i = 0; i < 7; i++) {
    requests.push(
      apiClient.post('/api/test/reset-game-state', {}).catch(error => error.response)
    );
  }
  
  try {
    const responses = await Promise.all(requests);
    let successCount = 0;
    let rateLimitCount = 0;
    
    responses.forEach((response, index) => {
      if (response.status === 200) {
        successCount++;
        console.log(`请求 ${index + 1}: ✅ 成功`);
      } else if (response.status === 429) {
        rateLimitCount++;
        console.log(`请求 ${index + 1}: ⚠️ 速率限制`);
      } else {
        console.log(`请求 ${index + 1}: ❌ 其他错误 (${response.status})`);
      }
    });
    
    console.log(`\n总结: 成功 ${successCount} 次, 速率限制 ${rateLimitCount} 次`);
    
    if (rateLimitCount > 0) {
      console.log('✅ 速率限制正常工作');
      return true;
    } else {
      console.log('⚠️ 速率限制可能没有生效');
      return false;
    }
  } catch (error) {
    console.error('❌ 速率限制测试失败:', error.message);
    return false;
  }
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log('🚀 开始测试重置游戏状态 API');
  console.log('🔑 请确保已设置有效的 JWT token');
  console.log('⚠️ 注意：重置操作会永久删除游戏数据');
  
  const results = {
    healthCheck: false,
    safetyInfo: false,
    resetGameState: false,
    rateLimit: false
  };

  // 运行测试
  results.healthCheck = await testHealthCheck();

  if (results.healthCheck) {
    results.safetyInfo = !!(await testGetResetSafetyInfo());
    results.resetGameState = !!(await testResetGameState());
    
    // 等待一下再测试速率限制
    console.log('\n⏳ 等待 2 秒后测试速率限制...');
    await new Promise(resolve => setTimeout(resolve, 2000));
    results.rateLimit = await testRateLimit();
  }
  
  // 输出测试结果
  console.log('\n📊 测试结果总结:');
  console.log('==================');
  Object.entries(results).forEach(([test, passed]) => {
    const status = passed ? '✅ 通过' : '❌ 失败';
    console.log(`${test}: ${status}`);
  });
  
  const passedCount = Object.values(results).filter(Boolean).length;
  const totalCount = Object.keys(results).length;
  
  console.log(`\n🎯 总体结果: ${passedCount}/${totalCount} 测试通过`);
  
  if (passedCount === totalCount) {
    console.log('🎉 所有测试都通过了！');
    process.exit(0);
  } else {
    console.log('⚠️ 部分测试失败，请检查实现');
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  runTests().catch(error => {
    console.error('💥 测试运行失败:', error);
    process.exit(1);
  });
}

module.exports = {
  testHealthCheck,
  testGetResetSafetyInfo,
  testResetGameState,
  testRateLimit
};
