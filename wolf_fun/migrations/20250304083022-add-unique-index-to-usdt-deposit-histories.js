'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    // 检查索引是否存在
    const tableIndexes = await queryInterface.showIndex('usdt_deposit_histories');
    const indexExists = tableIndexes.some(index => 
      index.name === 'usdt_deposit_histories_transaction_hash_unique'
    );

    if (!indexExists) {
      // 添加唯一索引到transactionHash字段
      await queryInterface.addIndex('usdt_deposit_histories', ['transactionHash'], {
        unique: true,
        name: 'usdt_deposit_histories_transaction_hash_unique'
      });
    }
  },

  async down (queryInterface, Sequelize) {
    // 检查索引是否存在
    const tableIndexes = await queryInterface.showIndex('usdt_deposit_histories');
    const indexExists = tableIndexes.some(index => 
      index.name === 'usdt_deposit_histories_transaction_hash_unique'
    );

    if (indexExists) {
      // 移除唯一索引
      await queryInterface.removeIndex('usdt_deposit_histories', 'usdt_deposit_histories_transaction_hash_unique');
    }
  }
};