'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      // 检查isStar列是否已存在
      const columns = await queryInterface.describeTable('user_wallets');
      if (!columns.isStar) {
        await queryInterface.addColumn('user_wallets', 'isStar', {
          type: Sequelize.BOOLEAN,
          allowNull: true,
          defaultValue: false
        });
        console.log('成功添加isStar字段到user_wallets表');
      } else {
        console.log('isStar字段已存在于user_wallets表中');
      }
    } catch (error) {
      console.error('迁移错误:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      // 检查isStar列是否存在
      const columns = await queryInterface.describeTable('user_wallets');
      if (columns.isStar) {
        await queryInterface.removeColumn('user_wallets', 'isStar');
        console.log('成功从user_wallets表中移除isStar字段');
      } else {
        console.log('isStar字段不存在于user_wallets表中');
      }
    } catch (error) {
      console.error('迁移回滚错误:', error);
      throw error;
    }
  }
};