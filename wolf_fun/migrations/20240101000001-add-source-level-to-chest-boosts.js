'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    try {
      // 获取所有表名
      const tables = await queryInterface.showAllTables();
      // console.log('数据库中的表:', tables);
      
      // 找到正确的表名
      const chestBoostTable = tables.find(table => 
        table.toLowerCase() === 'chest_boosts' || 
        table === 'ChestBoosts' || 
        table === 'chest_boosts'
      );
      
      if (!chestBoostTable) {
        console.log('找不到 chest_boosts 相关的表，尝试创建字段');
        // 如果表不存在，可能需要先创建表
        return;
      }
      
      // console.log('找到正确的表名:', chestBoostTable);
      
      // 使用正确的表名检查字段
      const table = await queryInterface.describeTable(chestBoostTable);
      if (!table.sourceLevel) {
        await queryInterface.addColumn(chestBoostTable, 'sourceLevel', {
          type: Sequelize.INTEGER.UNSIGNED,
          allowNull: false,
          defaultValue: 0,
          after: 'sourceWalletId'
        });
      }
    } catch (error) {
      console.error('迁移错误:', error);
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    try {
      const tables = await queryInterface.showAllTables();
      const chestBoostTable = tables.find(table => 
        table.toLowerCase() === 'chest_boosts' || 
        table === 'ChestBoosts' || 
        table === 'chest_boosts'
      );
      
      if (!chestBoostTable) {
        console.log('找不到 chest_boosts 相关的表，无法回滚');
        return;
      }
      
      const table = await queryInterface.describeTable(chestBoostTable);
      if (table.sourceLevel) {
        await queryInterface.removeColumn(chestBoostTable, 'sourceLevel');
      }
    } catch (error) {
      console.error('回滚错误:', error);
      throw error;
    }
  }
};