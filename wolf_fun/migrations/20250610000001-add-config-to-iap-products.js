'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      // 检查字段是否已经存在
      const tableDescription = await queryInterface.describeTable('iap_products');

      if (!tableDescription.config) {
        await queryInterface.addColumn('iap_products', 'config', {
          type: Sequelize.JSON,
          allowNull: true,
          comment: '产品特定配置，如VIP会员的加成比例、特殊套餐的内容等'
        });
        console.log('成功添加config字段到iap_products表');
      } else {
        console.log('config字段已存在于iap_products表中，跳过添加');
      }
    } catch (error) {
      console.error('添加config字段失败:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      // 检查字段是否存在
      const tableDescription = await queryInterface.describeTable('iap_products');

      if (tableDescription.config) {
        await queryInterface.removeColumn('iap_products', 'config');
        console.log('成功从iap_products表删除config字段');
      } else {
        console.log('config字段不存在于iap_products表中，跳过删除');
      }
    } catch (error) {
      console.error('删除config字段失败:', error);
      throw error;
    }
  }
};