-- 添加累积离线奖励字段到 user_wallets 表
-- 执行时间: 2025-07-14

-- 添加累积的离线宝石奖励字段
ALTER TABLE user_wallets 
ADD COLUMN accumulatedOfflineGems DECIMAL(65,3) DEFAULT 0 COMMENT '累积的离线宝石奖励';

-- 添加上次离线奖励计算时间字段
ALTER TABLE user_wallets 
ADD COLUMN lastOfflineRewardCalculation DATETIME NULL COMMENT '上次离线奖励计算时间';

-- 为新字段添加索引以提高查询性能
CREATE INDEX idx_user_wallets_accumulated_offline_gems ON user_wallets(accumulatedOfflineGems);
CREATE INDEX idx_user_wallets_last_offline_calculation ON user_wallets(lastOfflineRewardCalculation);
