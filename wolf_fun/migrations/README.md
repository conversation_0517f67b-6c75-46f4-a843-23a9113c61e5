# 数据库迁移文档

## 累积离线奖励功能迁移

### 迁移文件
- **文件名**: `20250714-add-accumulated-offline-rewards.js`
- **创建日期**: 2025-07-14
- **功能**: 为累积离线奖励功能添加必要的数据库字段和索引

### 新增字段

#### user_wallets 表

1. **accumulatedOfflineGems**
   - 类型: `DECIMAL(65,3)`
   - 默认值: `0`
   - 允许空值: `否`
   - 注释: `累积的离线宝石奖励`
   - 用途: 存储用户累积但未领取的离线宝石奖励

2. **lastOfflineRewardCalculation**
   - 类型: `DATETIME`
   - 默认值: `NULL`
   - 允许空值: `是`
   - 注释: `上次离线奖励计算时间`
   - 用途: 记录上次计算离线奖励的时间，用于增量计算

### 新增索引

1. **idx_user_wallets_accumulated_offline_gems**
   - 字段: `accumulatedOfflineGems`
   - 用途: 提高查询累积奖励的性能

2. **idx_user_wallets_last_offline_calculation**
   - 字段: `lastOfflineRewardCalculation`
   - 用途: 提高基于计算时间查询的性能

## 使用方法

### 执行迁移
```bash
# 使用 npm 脚本
npm run migrate:offline-rewards

# 或直接运行
node run_sequelize_migration.js
```

### 回滚迁移
```bash
# 使用 npm 脚本
npm run migrate:offline-rewards:rollback

# 或直接运行
node rollback_sequelize_migration.js
```

## 迁移特性

### 智能检测
- 迁移脚本会自动检测字段和索引是否已存在
- 如果已存在，会跳过创建，避免重复执行错误
- 支持安全的重复执行

### 事务支持
- 所有操作都在数据库事务中执行
- 如果任何步骤失败，会自动回滚所有更改
- 确保数据库状态的一致性

### 详细日志
- 提供详细的执行日志
- 清楚显示每个步骤的执行状态
- 便于调试和监控

## 相关功能

这个迁移支持以下功能：

1. **累积离线奖励系统**
   - 用户离线期间的奖励会持续累积
   - 支持分批领取，不会丢失奖励

2. **智能计算**
   - 基于时间间隔进行增量计算
   - 避免重复计算，提高性能

3. **数据持久化**
   - 累积奖励安全存储在数据库中
   - 支持服务器重启后的数据恢复

## 注意事项

1. **数据精度**: 使用 `DECIMAL(65,3)` 确保大数值的精确存储
2. **索引优化**: 添加的索引会提高查询性能，但会略微增加写入开销
3. **向后兼容**: 新字段设计为可选，不影响现有功能
4. **安全执行**: 支持重复执行，不会破坏现有数据

## 测试验证

迁移完成后，可以通过以下方式验证：

```sql
-- 检查字段是否存在
SHOW COLUMNS FROM user_wallets LIKE 'accumulatedOfflineGems';
SHOW COLUMNS FROM user_wallets LIKE 'lastOfflineRewardCalculation';

-- 检查索引是否存在
SHOW INDEX FROM user_wallets WHERE Key_name = 'idx_user_wallets_accumulated_offline_gems';
SHOW INDEX FROM user_wallets WHERE Key_name = 'idx_user_wallets_last_offline_calculation';
```
