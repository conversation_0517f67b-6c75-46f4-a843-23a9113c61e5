// src/config/redis.ts
import Redis from 'ioredis';
import dotenv from 'dotenv';

dotenv.config();

export const redis = new Redis({
  host: process.env.REDIS_HOST || 'redis',
  port: parseInt(process.env.REDIS_PORT || '6379', 10),
  password: process.env.REDIS_PASS || '',
  maxRetriesPerRequest: null
});

redis.on('connect', () => {
  console.log('Connected to Redis.');
});

redis.on('error', (err) => {
  console.error('Redis error:', err);
});