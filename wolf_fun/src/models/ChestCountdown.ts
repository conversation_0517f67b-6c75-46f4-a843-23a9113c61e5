import { Model, DataTypes, Optional } from "sequelize";
import { sequelize } from "../config/db";
import { UserWallet } from "./UserWallet";

interface ChestCountdownAttributes {
  id: number;
  userId: number;
  walletId: number;
  nextAvailableTime: Date; // 下一次可领取宝箱的时间
  autoCollect: boolean; // 是否开启自动领取
  createdAt?: Date;
  updatedAt?: Date;
}

type ChestCountdownCreationAttributes = Optional<ChestCountdownAttributes, "id">;

export class ChestCountdown
  extends Model<ChestCountdownAttributes, ChestCountdownCreationAttributes>
  implements ChestCountdownAttributes
{
  public id!: number;
  public userId!: number;
  public walletId!: number;
  public nextAvailableTime!: Date;
  public autoCollect!: boolean;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;
}

ChestCountdown.init(
  {
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    userId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
    },
    walletId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
    },
    nextAvailableTime: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    autoCollect: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    },
  },
  {
    tableName: "chest_countdowns",
    sequelize,
    timestamps: true,
  }
);

// 建立与UserWallet的关联
ChestCountdown.belongsTo(UserWallet, {
  foreignKey: 'walletId',
  as: 'userWallet'
});