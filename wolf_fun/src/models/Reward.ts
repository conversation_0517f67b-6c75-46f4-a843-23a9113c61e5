// src/models/Reward.ts
import { Model, DataTypes, Optional } from "sequelize";
import { sequelize } from "../config/db";

interface RewardAttributes {
  id: number;
  userId: number;
  walletId: number;
  rewardType: string; // ticket, ton, gem
  amount: number; // 数量或金额
  lockedDays?: number; // 365天线性解锁的情况下，可用此字段记录剩余锁定天数
}

type RewardCreationAttributes = Optional<RewardAttributes, "id">;

export class Reward
  extends Model<RewardAttributes, RewardCreationAttributes>
  implements RewardAttributes
{
  public id!: number;
  public userId!: number;
  public walletId!: number;
  public rewardType!: string;
  public amount!: number;
  public lockedDays!: number;
}

Reward.init(
  {
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    userId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
    },
    walletId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
    },
    rewardType: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    amount: {
      type: DataTypes.DECIMAL(18, 6),
      defaultValue: 0,
      allowNull: false,
    },
    lockedDays: {
      type: DataTypes.INTEGER.UNSIGNED,
      defaultValue: 0,
    },
  },
  {
    tableName: "rewards",
    sequelize,
    timestamps: true,
  }
);
