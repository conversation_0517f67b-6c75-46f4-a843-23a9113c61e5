import { Model, DataTypes, Optional } from 'sequelize';
import { sequelize } from '../config/db';

interface FarmConfigAttributes {
  id: number;
  grade: number;
  production: number;
  cow: number;
  speed: number;
  milk: number;
  cost: number;
  offline: number;
  version: string;
  isActive: boolean;
  createdBy?: string;
  remark?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

interface FarmConfigCreationAttributes extends Optional<FarmConfigAttributes, 'id' | 'createdAt' | 'updatedAt' | 'createdBy' | 'remark'> {}

class FarmConfig extends Model<FarmConfigAttributes, FarmConfigCreationAttributes> implements FarmConfigAttributes {
  public id!: number;
  public grade!: number;
  public production!: number;
  public cow!: number;
  public speed!: number;
  public milk!: number;
  public cost!: number;
  public offline!: number;
  public version!: string;
  public isActive!: boolean;
  public createdBy?: string;
  public remark?: string;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  /**
   * 获取指定版本的所有配置
   */
  public static async getConfigsByVersion(version: string): Promise<FarmConfig[]> {
    return await FarmConfig.findAll({
      where: { version },
      order: [['grade', 'ASC']]
    });
  }

  /**
   * 获取当前激活的配置
   */
  public static async getActiveConfigs(): Promise<FarmConfig[]> {
    return await FarmConfig.findAll({
      where: { isActive: true },
      order: [['grade', 'ASC']]
    });
  }

  /**
   * 获取指定等级的激活配置
   */
  public static async getActiveConfigByGrade(grade: number): Promise<FarmConfig | null> {
    return await FarmConfig.findOne({
      where: { 
        grade,
        isActive: true 
      }
    });
  }

  /**
   * 获取所有版本列表
   */
  public static async getAllVersions(): Promise<string[]> {
    const results = await FarmConfig.findAll({
      attributes: ['version'],
      group: ['version'],
      order: [['createdAt', 'DESC']]
    });
    return results.map(r => r.version);
  }

  /**
   * 激活指定版本的配置
   */
  public static async activateVersion(version: string): Promise<boolean> {
    const transaction = await sequelize.transaction();
    
    try {
      // 先将所有配置设为非激活状态
      await FarmConfig.update(
        { isActive: false },
        { 
          where: {},
          transaction 
        }
      );

      // 激活指定版本的配置
      const [affectedCount] = await FarmConfig.update(
        { isActive: true },
        { 
          where: { version },
          transaction 
        }
      );

      await transaction.commit();
      return affectedCount > 0;
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * 删除指定版本的配置
   */
  public static async deleteVersion(version: string): Promise<boolean> {
    // 检查是否为激活版本
    const activeConfig = await FarmConfig.findOne({
      where: { version, isActive: true }
    });

    if (activeConfig) {
      throw new Error('无法删除当前激活的配置版本');
    }

    const deletedCount = await FarmConfig.destroy({
      where: { version }
    });

    return deletedCount > 0;
  }

  /**
   * 批量创建配置数据
   */
  public static async bulkCreateConfigs(
    configs: FarmConfigCreationAttributes[],
    version: string,
    createdBy?: string
  ): Promise<FarmConfig[]> {
    const transaction = await sequelize.transaction();
    
    try {
      // 为所有配置添加版本和创建者信息
      const configsWithMeta = configs.map(config => ({
        ...config,
        version,
        createdBy,
        isActive: false
      }));

      const createdConfigs = await FarmConfig.bulkCreate(configsWithMeta, {
        transaction,
        validate: true
      });

      await transaction.commit();
      return createdConfigs;
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * 验证配置数据完整性
   */
  public static validateConfigData(configs: any[]): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (!Array.isArray(configs) || configs.length === 0) {
      errors.push('配置数据不能为空');
      return { isValid: false, errors };
    }

    // 检查是否包含0-50级的完整配置
    const expectedGrades = Array.from({ length: 51 }, (_, i) => i);
    const actualGrades = configs.map(c => c.grade).sort((a, b) => a - b);
    
    if (JSON.stringify(expectedGrades) !== JSON.stringify(actualGrades)) {
      errors.push('配置数据必须包含0-50级的完整配置');
    }

    // 验证每个配置的字段
    configs.forEach((config, index) => {
      const requiredFields = ['grade', 'production', 'cow', 'speed', 'milk', 'cost', 'offline'];
      
      requiredFields.forEach(field => {
        if (config[field] === undefined || config[field] === null) {
          errors.push(`第${index + 1}行缺少必需字段: ${field}`);
        }
      });

      // 验证数值范围
      if (config.grade < 0 || config.grade > 50) {
        errors.push(`第${index + 1}行等级超出范围 (0-50): ${config.grade}`);
      }

      if (config.production < 0) {
        errors.push(`第${index + 1}行产出值不能为负数: ${config.production}`);
      }

      if (config.cow < 0) {
        errors.push(`第${index + 1}行奶牛数量不能为负数: ${config.cow}`);
      }

      if (config.speed < 0) {
        errors.push(`第${index + 1}行速度不能为负数: ${config.speed}`);
      }

      if (config.milk < 0) {
        errors.push(`第${index + 1}行牛奶产量不能为负数: ${config.milk}`);
      }

      if (config.cost < 0) {
        errors.push(`第${index + 1}行升级费用不能为负数: ${config.cost}`);
      }

      if (config.offline < 0) {
        errors.push(`第${index + 1}行离线产出不能为负数: ${config.offline}`);
      }
    });

    return { isValid: errors.length === 0, errors };
  }
}

FarmConfig.init(
  {
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    grade: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      comment: '等级 (0-50)',
    },
    production: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      comment: '每秒产出计算用值',
    },
    cow: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      comment: '奶牛数量',
    },
    speed: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      comment: '生产速度百分比',
    },
    milk: {
      type: DataTypes.DECIMAL(15, 3),
      allowNull: false,
      comment: '牛奶生产',
    },
    cost: {
      type: DataTypes.BIGINT.UNSIGNED,
      allowNull: false,
      comment: '升级花费',
    },
    offline: {
      type: DataTypes.DECIMAL(15, 3),
      allowNull: false,
      comment: '离线产出',
    },
    version: {
      type: DataTypes.STRING(50),
      allowNull: false,
      defaultValue: 'default',
      comment: '配置版本',
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: '是否激活',
    },
    createdBy: {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: '创建者',
    },
    remark: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '版本说明',
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize,
    tableName: 'farm_configs',
    timestamps: true,
    indexes: [
      {
        fields: ['grade']
      },
      {
        fields: ['version']
      },
      {
        fields: ['isActive']
      },
      {
        fields: ['createdAt']
      },
      {
        unique: true,
        fields: ['grade', 'version']
      }
    ]
  }
);

export { FarmConfig };
