// src/models/MonitorWalletAddress.ts
import { Model, DataTypes, Optional } from "sequelize";
import { sequelize } from "../config/db";

interface MonitorWalletAddressAttributes {
  id: number;
  walletAddress: string;
  parsedWalletAddress: string;
  description: string;
  isActive: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

type MonitorWalletAddressCreationAttributes = Optional<MonitorWalletAddressAttributes, "id">;

export class MonitorWalletAddress
  extends Model<MonitorWalletAddressAttributes, MonitorWalletAddressCreationAttributes>
  implements MonitorWalletAddressAttributes
{
  public id!: number;
  public walletAddress!: string;
  public parsedWalletAddress!: string;
  public description!: string;
  public isActive!: boolean;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;
}

MonitorWalletAddress.init(
  {
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    walletAddress: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
    },
    parsedWalletAddress: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
    },
    description: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      allowNull: false,
    },
  },
  {
    sequelize,
    tableName: "monitor_wallet_addresses",
    timestamps: true,
  }
);