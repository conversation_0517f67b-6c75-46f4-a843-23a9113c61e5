// src/models/UserTaskComplete.ts
import { Model, DataTypes, Optional } from "sequelize";
import { sequelize } from "../config/db";

interface UserTaskCompleteAttributes {
  id: number;
  userId: number; // 哪个用户
  taskId: number; // 哪个任务
  walletId: number; // 哪个钱包
  completeTime: Date;
  createdAt?: Date;
  updatedAt?: Date;
}

type UserTaskCompleteCreationAttributes = Optional<
  UserTaskCompleteAttributes,
  "id" | "completeTime"
>;

export class UserTaskComplete
  extends Model<UserTaskCompleteAttributes, UserTaskCompleteCreationAttributes>
  implements UserTaskCompleteAttributes
{
  public id!: number;
  public userId!: number;
  public walletId!: number;
  public taskId!: number;
  public completeTime!: Date;

  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;
}

UserTaskComplete.init(
  {
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    userId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
    },
    walletId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
    },
    taskId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
    },
    completeTime: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    tableName: "user_task_complete",
    sequelize,
    timestamps: true,
  }
);
