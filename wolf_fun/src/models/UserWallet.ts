// src/models/UserWallet.ts

import { Model, DataTypes, Optional } from "sequelize";
import { sequelize } from "../config/db";

interface UserWalletAttributes {
  id: number;
  userId: number; // 属于哪个User
  code?: string;
  ton?: number | string;
  gem?: number | string;
  ticket?: number;
  free_ticket?: number; // 新增：免费门票
  fragment_green?: number;
  fragment_blue?: number;
  fragment_purple?: number;
  fragment_gold?: number;
  usd?: number;
  moof?: number;
  unlockMoof?: number;// 解锁的moof
  bullReward?: number; // 金牛奖励
  winRate?: number;
  walletAddress?: string;
  parsedWalletAddress?: string;
  referrerWalletId?: number; // 新增：推荐人钱包ID
  referralCount?: number; // 新增：推荐数量
  network?: string;
  isStar?: boolean; // 用户是否为Star用户
  milk: number; // 牛奶，牧场区产出的资源
  hasCollectedFourChests?: boolean; // 新增：是否已领取四个宝箱
  lastActiveTime?: Date; // 新增：最后活跃时间，用于计算离线奖励
  accumulatedOfflineGems?: number; // 新增：累积的离线宝石奖励
  lastOfflineRewardCalculation?: Date; // 新增：上次离线奖励计算时间

}

type UserWalletCreationAttributes = Optional<UserWalletAttributes, "id">;

export class UserWallet
  extends Model<UserWalletAttributes, UserWalletCreationAttributes>
  implements UserWalletAttributes
{
  public id!: number;
  public userId!: number;
  public code?: string;
  public ton?: number | string;
  public winRate?: number;
  public gem?: number | string;
  public ticket?: number;
  public free_ticket?: number; // 新增：免费门票
  public fragment_green?: number;
  public fragment_blue?: number;
  public fragment_purple?: number;
  public fragment_gold?: number;
  public usd?: number;
  public moof?: number;
  public unlockMoof?: number;
  public bullReward?: number;
  public walletAddress?: string;
  public referrerWalletId?: number; // 新增：推荐人钱包ID
  public referralCount?: number; // 新增：推荐数量
  public network?: string;
  public isStar?: boolean; // 用户是否为Star用户
  public milk!: number; // 牛奶，牧场区产出的资源
  public hasCollectedFourChests?: boolean; // 是否已领取四个宝箱
  public lastActiveTime?: Date; // 新增：最后活跃时间，用于计算离线奖励
  public accumulatedOfflineGems?: number; // 新增：累积的离线宝石奖励
  public lastOfflineRewardCalculation?: Date; // 新增：上次离线奖励计算时间
  //经过Address.parse 转化后的地址
  parsedWalletAddress?: string;

  // timestamps
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;
}

UserWallet.init(
  {
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    userId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
    },
    code: DataTypes.STRING,

    walletAddress: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    parsedWalletAddress: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    
    winRate: {
      type: DataTypes.FLOAT,
      defaultValue: 1, // 默认胜率为 1，即每个玩家有相等的概率
    },
    
    ton: {
      type: DataTypes.DECIMAL(65, 3), // 支持超大数值
      defaultValue: 0,
    },
    gem: {
      type: DataTypes.DECIMAL(65, 3), // 支持超大数值
      defaultValue: 0,
    },
    ticket: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    free_ticket: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    fragment_green: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    fragment_blue: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    fragment_purple: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    fragment_gold: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    usd: {
      type: DataTypes.DECIMAL(65, 3), // 支持超大数值
      defaultValue: 0,
    },
    moof: {
      type: DataTypes.DECIMAL(65, 3), // 支持超大数值
      defaultValue: 0,
    },
    unlockMoof: {
      type: DataTypes.DECIMAL(65, 3), // 支持超大数值
      defaultValue: 0,
    },
    bullReward: {
      type: DataTypes.DECIMAL(65, 3), // 支持超大数值
      defaultValue: 0,
    },
    referrerWalletId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: true,
    },
    referralCount: {
      type: DataTypes.INTEGER.UNSIGNED,
      defaultValue: 0,
    },
    network: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    milk: {
      type: DataTypes.DECIMAL(65, 3), // 支持超大数值
      allowNull: false,
      defaultValue: 0,
    },
    isStar: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: true,
    },
    hasCollectedFourChests: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: true,
    },
    lastActiveTime: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    accumulatedOfflineGems: {
      type: DataTypes.DECIMAL(65, 3), // 支持超大数值
      defaultValue: 0,
      allowNull: true,
    },
    lastOfflineRewardCalculation: {
      type: DataTypes.DATE,
      allowNull: true,
    },

  },
  {
    tableName: "user_wallets",
    sequelize,
    timestamps: true,
  }
);
