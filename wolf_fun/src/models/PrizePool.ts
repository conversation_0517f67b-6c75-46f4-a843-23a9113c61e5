import { Model, DataTypes, Optional } from "sequelize";
import { sequelize } from "../config/db";

export interface PrizePoolAttributes {
  id: number;
  type: string;      // 奖池类型，例如 "moof_holders", "personal_kol", "bull_king" 等
  amount: number;    // 奖池当前金额
  createdAt?: Date;
  updatedAt?: Date;
}

export type PrizePoolCreationAttributes = Optional<PrizePoolAttributes, "id">;

export class PrizePool
  extends Model<PrizePoolAttributes, PrizePoolCreationAttributes>
  implements PrizePoolAttributes
{
  public id!: number;
  public type!: string;
  public amount!: number;

  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;
}

PrizePool.init(
  {
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    type: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true, // 确保奖池类型唯一
    },
    amount: {
      type: DataTypes.FLOAT,
      allowNull: false,
      defaultValue: 0,
    },
  },
  {
    tableName: "prize_pools",
    sequelize,
    timestamps: true,
  }
);

export default PrizePool;