import { Router, Request, Response } from "express";
import {
  Room,
  Session,
  UserWallet,
  Reservation,
  Round,
  WalletHistory,
  GameHistory,
  PrizePool, // 新增导入
} from "../models";
import { redis } from "../config/redis";
import { MyRequest } from "../types/customRequest";
import dayjs from "dayjs";
import { walletAuthMiddleware } from "../middlewares/walletAuth";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import { v4 as uuidv4 } from "uuid";
import { createWalletHistory } from "../services/walletHistoryService";
import { createGameHistory } from "../services/gameHistoryService";
import { DataValidationCxt } from "ajv/dist/types";
import { GAME_CONFIG } from "../config/consts";
import { Op, literal } from "sequelize";
import { ajv, tFromRequest, formatValidationErrors, t } from "../i18n";
import { languageMiddleware } from "../middlewares/languageMiddleware";
import { successResponse, errorResponse } from "../utils/responseUtil";
import { getCurrentRequestLanguage } from "../i18n/requestContext";
// 移除 zod 导入，改用 ajv

dayjs.extend(utc);
dayjs.extend(timezone);

const router = Router();
const TZ = "Asia/Shanghai";

// 应用语言中间件到所有路由
router.use(languageMiddleware);

// 辅助函数：为 Redis 命令增加超时控制
function redisCommand<T>(
  promise: Promise<T>,
  timeoutMs = 5000,
  errorMessage = "Redis 命令超时"
): Promise<T> {
  return Promise.race([
    promise,
    new Promise<T>((_, reject) =>
      setTimeout(() => reject(new Error(errorMessage)), timeoutMs)
    ),
  ]);
}

/**
 * 获取 Redis 分布式锁（TTL 单位：毫秒）
 */
async function acquireLock(key: string, ttl: number): Promise<string | null> {
  const token = uuidv4();
  const result = await redisCommand(
    (redis.set as any)(key, token, "NX", "PX", ttl),
    5000,
    `获取 Redis 锁 ${key} 超时`
  );
  if (result === "OK") {
    return token;
  }
  return null;
}

/**
 * 释放 Redis 锁，只有持有该 token 的才能释放
 */
async function releaseLock(key: string, token: string): Promise<void> {
  const script = `
    if redis.call("get", KEYS[1]) == ARGV[1] then
      return redis.call("del", KEYS[1])
    else
      return 0
    end
  `;
  await redisCommand(
    redis.eval(script, 1, key, token),
    5000,
    `释放 Redis 锁 ${key} 超时`
  );
}

/**
 * 根据 session_dt 与 session_category 获取场次记录
 */
async function getSession(session_dt: string, session_category: string) {
  const sessionDate = dayjs.tz(session_dt, "YYYY-MM-DD HH:mm:ss", TZ).toDate();
  return Session.findOne({
    where: {
      session_dt: {
        [Op.gte]: dayjs(sessionDate).startOf("day").toDate(),
        [Op.lte]: dayjs(sessionDate).endOf("day").toDate(),
      },
      session_category,
    },
    order: [["session_dt", "DESC"]],
  });
}

/**
 * 验证单个回合是否可预约
 */
async function validateRound(
  sessionRecord: any,
  roundIndex: number,
  userId: number,
  currentTime: dayjs.Dayjs
): Promise<string | null> {
  if (roundIndex < 1 || roundIndex > 3) {
    return `无效的 roundIndex ${roundIndex}，必须在 1 到 3 之间`;
  }
  const existingReservation = await Reservation.findOne({
    where: { sessionId: sessionRecord.id, userId, roundIndex },
  });
  if (existingReservation) {
    return `您已预约场次 ${sessionRecord.session_category} ${sessionRecord.session_dt} 的第 ${roundIndex} 回合，不能重复预约`;
  }
  const roundRecord = await Round.findOne({
    where: { sessionId: sessionRecord.id, roundIndex },
  });
  if (!roundRecord) {
    return `场次 ${sessionRecord.session_category} ${sessionRecord.session_dt} 的第 ${roundIndex} 回合数据未生成，不可预约`;
  }
  const cutoffTime = dayjs(roundRecord.result_time).tz(TZ).subtract(2, "minute");
  if (currentTime.isAfter(cutoffTime)) {
    return `场次 ${sessionRecord.session_category} ${sessionRecord.session_dt} 的第 ${roundIndex} 回合预约时间已截止`;
  }
  return null;
}

/**
 * 验证同一场次下多个回合的预约
 */
async function validateRoundsForSession(
  sessionRecord: any,
  rounds: number[],
  userId: number,
  currentTime: dayjs.Dayjs
): Promise<string | null> {
  const errors = await Promise.all(
    rounds.map((roundIndex) =>
      validateRound(sessionRecord, roundIndex, userId, currentTime)
    )
  );
  return errors.find((msg) => msg !== null) || null;
}

/**
 * 分配房间
 */
async function assignRoomForRound(
  sessionId: number,
  roundIndex: number,
  transaction: any
): Promise<{ room: any; roomId: string; roomNumber: number }> {
  const lockKey = `lock:session:${sessionId}:round:${roundIndex}:room`;
  const token = await acquireLock(lockKey, 30000);
  if (!token) {
    throw new Error(tFromRequest(null, "errors.roomLockFailed"));
  }
  try {
    const currentRoomKey = `session:${sessionId}:round:${roundIndex}:currentRoomId`;
    let roomId: string | null;
    try {
      roomId = await redisCommand(
        redis.get(currentRoomKey),
        5000,
        `获取 ${currentRoomKey} 超时`
      );
    } catch (err) {
      throw new Error(tFromRequest(null, "errors.getRoomIdFailed", { error: err }));
    }
    if (!roomId) {
      const room = await Room.create(
        {
          sessionId,
          roundIndex,
          isFull: false,
        },
        { transaction }
      );
      await redisCommand(
        redis.set(currentRoomKey, room.id.toString()),
        5000,
        `设置当前房间标识 ${currentRoomKey} 超时`
      );
      await redisCommand(
        redis.set(`room:${room.id}:count`, "0"),
        5000,
        `初始化房间计数器 room:${room.id}:count 超时`
      );
      roomId = room.id.toString();
    }
    const roomCountKey = `room:${roomId}:count`;
    const luaScript = `
      local current = redis.call("get", KEYS[1])
      if current == nil then
        redis.call("set", KEYS[1], 1)
        return 1
      else
        current = tonumber(current)
        if current < tonumber(ARGV[1]) then
          local newVal = redis.call("incr", KEYS[1])
          if newVal > tonumber(ARGV[1]) then
            redis.call("decr", KEYS[1])
            return 0
          end
          return newVal
        else
          return 0
        end
      end
    `;
    let result;
    try {
      result = await redisCommand(
        redis.eval(luaScript, 1, roomCountKey, GAME_CONFIG.MAX_PARTICIPANTS),
        5000,
        `执行 Lua 脚本超时: roomCountKey=${roomCountKey}`
      );
    } catch (err) {
      throw new Error(tFromRequest(null, "errors.luaScriptFailed", { error: err }));
    }
    let count = Number(result);
    if (count > GAME_CONFIG.MAX_PARTICIPANTS) {
      console.error(
        `[房间分配] 房间 ${roomId} 的人数 ${count} 超过最大值 ${GAME_CONFIG.MAX_PARTICIPANTS}`
      );
      throw new Error(tFromRequest(null, "errors.roomExceedsLimit", { roomId, count }));
    }
    if (count === 0) {
      const newRoom = await Room.create(
        {
          sessionId,
          roundIndex,
          isFull: false,
        },
        { transaction }
      );
      roomId = newRoom.id.toString();
      await redisCommand(
        redis.set(currentRoomKey, roomId),
        5000,
        `设置新房间标识 ${currentRoomKey} 超时`
      );
      await redisCommand(
        redis.set(`room:${roomId}:count`, "1"),
        5000,
        `设置新房间计数器超时: room:${roomId}:count`
      );
      count = 1;
    }
    const isFull = count === GAME_CONFIG.MAX_PARTICIPANTS;
    await Room.update({ isFull }, { where: { id: roomId }, transaction });
    const room = await Room.findByPk(roomId, { transaction });
    if (!room) {
      throw new Error(tFromRequest(null, "errors.roomAllocationFailed"));
    }
    console.log(`[房间分配] 房间 ${roomId} 分配成功，当前人数: ${count}`);
    return { room, roomId, roomNumber: count };
  } finally {
    await releaseLock(lockKey, token);
  }
}

// 定义预约请求验证模式
// 添加自定义关键字，验证session_dt是否在今天范围内
ajv.addKeyword({
  keyword: "isToday",
  validate: function validate(schema: boolean, data: string) {
    if (!schema) return true;
    
    // 验证格式是否为 YYYY-MM-DD HH:mm:ss
    if (!/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(data)) {
      (validate as any).errors = [{
        keyword: "isToday",
        message: t("errors.invalidDateFormat", { format: "YYYY-MM-DD HH:mm:ss" })
      }];
      return false;
    }
    
    // 验证日期是否在今天范围内
    const inputDate = dayjs.tz(data, "YYYY-MM-DD HH:mm:ss", TZ);
    const today = dayjs().tz(TZ);
    
    if (!inputDate.isValid()) {
      (validate as any).errors = [{
        keyword: "isToday",
        message: t("errors.invalidDate")
      }];
      return false;
    }
    
    if (inputDate.date() !== today.date() || 
        inputDate.month() !== today.month() || 
        inputDate.year() !== today.year()) {
      (validate as any).errors = [{
        keyword: "isToday",
        message: t("errors.dateNotToday")
      }];
      return false;
    }
    
    return true;
  },
  errors: true
});

// 添加自定义关键字，验证tickets数组长度与rounds数组长度一致
ajv.addKeyword({
  keyword: "ticketsMatchRounds",
  validate: function validate(schema: boolean, data: any, parentSchema: any, dataCxt?: DataValidationCxt) {
    if (!schema) return true;
    
    // 获取当前验证的对象
    // 注意：parentData可能是数组中的一个元素，而不是整个数组
    const currentData = data;
    if (!currentData) return true;
    
    const rounds = currentData.rounds;
    const tickets = currentData.tickets;
    
    if (!Array.isArray(tickets)) {
      (validate as any).errors = [{
        keyword: "ticketsMatchRounds",
        message: t("errors.ticketsNotArray")
      }];
      return false;
    }
    
    if (!Array.isArray(rounds)) {
      (validate as any).errors = [{
        keyword: "ticketsMatchRounds",
        message: t("errors.roundsNotArray")
      }];
      return false;
    }
    
    if (tickets.length !== rounds.length) {
      (validate as any).errors = [{
        keyword: "ticketsMatchRounds",
        message: t("errors.ticketsLengthMismatch")
      }];
      return false;
    }
    
    // 验证tickets数组中的每个元素是否为有效的票类型
    const validTicketTypes = ["ticket", "free_ticket"];
    for (let i = 0; i < tickets.length; i++) {
      if (!validTicketTypes.includes(tickets[i])) {
        (validate as any).errors = [{
          keyword: "ticketsMatchRounds",
          message: t("errors.invalidTicketType", { type: tickets[i], validTypes: validTicketTypes.join(", ") })
        }];
        return false;
      }
    }
    
    return true;
  },
  errors: true
});

const reserveRequestSchema = {
  type: "object",
  properties: {
    sessions: {
      type: "array",
      items: {
        type: "object",
        properties: {
          session_dt: {
            type: "string",
            pattern: "^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}$",
            isToday: true
          },
          session_category: {
            type: "string",
            minLength: 1
          },
          rounds: {
            type: "array",
            items: {
              type: "integer",
              enum: [1, 2, 3]
            },
            minItems: 1
          },
          tickets: {
            type: "array",
            items: {
              type: "string",
              enum: ["ticket", "free_ticket"]
            },
            minItems: 1
          }
        },
        required: ["session_dt", "session_category", "rounds", "tickets"],
        ticketsMatchRounds: true,
        additionalProperties: false
      },
      minItems: 1
    }
  },
  required: ["sessions"],
  additionalProperties: false
};

const validateReserveRequest = ajv.compile(reserveRequestSchema);

/**
 * 预定接口
 */
router.post(
  "/reserve",
  walletAuthMiddleware,
  //@ts-ignore
  async (req: Request, res: Response) => {
    let userLockToken: string | null = null;
    const myReq = req as MyRequest;
    const { userId, walletId } = myReq.user!;
    console.log(`[预约请求] 用户 ${userId} 开始预约处理`);
    const userLockKey = `lock:user:${walletId}:reserve`;
    
    // 验证请求参数
    const validate = validateReserveRequest;
    const valid = validate(req.body);
    if (!valid) {
      return res.status(400).json(errorResponse(
        tFromRequest(req, "errors.paramValidation"),
        formatValidationErrors(validate.errors || [], req.language)
      ));
    }
    try {
      if (!userId || !walletId) {
        return res.status(400).json(errorResponse(tFromRequest(req, "errors.missingUserOrWalletId")));
      }
      userLockToken = await acquireLock(userLockKey, 50000);
      if (!userLockToken) {
        console.warn(`[预约请求] 用户 ${userId} 有其他请求正在处理中`);
        return res
          .status(429)
          .json(
            errorResponse(tFromRequest(req, "errors.requestInProgress"))
          );
      }
      const { sessions: sessionsReq } = req.body;
      if (!Array.isArray(sessionsReq) || sessionsReq.length === 0) {
        console.warn(`[预约请求] 用户 ${userId} 提交了无效的请求参数`);
        return res
          .status(400)
          .json(errorResponse(tFromRequest(req, "errors.noSessionsProvided")));
      }
      const currentTime = dayjs().tz(TZ);
      let totalRoundsRequested = 0;
      const processedSessions: Array<{
        sessionRecord: any;
        rounds: number[];
        tickets: string[];
      }> = [];
      for (const sessReq of sessionsReq) {
        const { session_dt, session_category, rounds, tickets } = sessReq;
        if (
          !session_dt ||
          !session_category ||
          !Array.isArray(rounds) ||
          rounds.length === 0 ||
          !Array.isArray(tickets) ||
          tickets.length === 0 ||
          tickets.length !== rounds.length
        ) {
          console.warn(
            `[预约请求] 用户 ${userId} 缺少场次信息或tickets参数不匹配: session_dt=${session_dt}, session_category=${session_category}`
          );
          return res
            .status(400)
            .json(
              errorResponse(tFromRequest(req, "errors.invalidSessionFormat"))
            );
        }
        const sessionRecord = await getSession(session_dt, session_category);
        if (!sessionRecord) {
          console.warn(
            `[预约请求] 用户 ${userId} 请求的场次不存在: ${session_category} ${session_dt}`
          );
          return res
            .status(404)
            .json(
              errorResponse(tFromRequest(req, "errors.sessionNotFound", { category: session_category, date: session_dt }))
            );
        }
        const validationError = await validateRoundsForSession(
          sessionRecord,
          rounds,
          userId,
          currentTime
        );
        if (validationError) {
          console.warn(
            `[预约请求] 用户 ${userId} 回合验证失败: ${validationError}`
          );
          return res.status(400).json(errorResponse(validationError));
        }
        totalRoundsRequested += rounds.length;
        processedSessions.push({ sessionRecord, rounds, tickets });
      }
      // 计算需要扣减的普通票和免费票数量
      let totalNormalTickets = 0;
      let totalFreeTickets = 0;
      
      for (const session of processedSessions) {
        for (let i = 0; i < session.rounds.length; i++) {
          if (session.tickets[i] === 'ticket') {
            totalNormalTickets += GAME_CONFIG.TICKET_COST;
          } else if (session.tickets[i] === 'free_ticket') {
            totalFreeTickets += GAME_CONFIG.TICKET_COST;
          }
        }
      }
      
      const totalUsdCost = totalRoundsRequested * GAME_CONFIG.USD_COST;
      const sequelizeInstance = UserWallet.sequelize;
      if (!sequelizeInstance) {
        console.error(`[预约请求] 用户 ${userId} 数据库连接错误`);
        return res.status(500).json(errorResponse(tFromRequest(req, "errors.databaseConnectionError")));
      }
      const transaction = await sequelizeInstance.transaction();
      try {
        // 构建更新条件
        const updateFields: any = {
          usd: literal(`usd - ${totalUsdCost}`),
        };
        
        // 构建查询条件
        const whereCondition: any = {
          id: walletId,
          usd: { [Op.gte]: totalUsdCost },
        };
        
        // 根据需要扣减的票类型添加相应的更新和条件
        if (totalNormalTickets > 0) {
          updateFields.ticket = literal(`ticket - ${totalNormalTickets}`);
          whereCondition.ticket = { [Op.gte]: totalNormalTickets };
        }
        
        if (totalFreeTickets > 0) {
          updateFields.free_ticket = literal(`free_ticket - ${totalFreeTickets}`);
          whereCondition.free_ticket = { [Op.gte]: totalFreeTickets };
        }
        
        const [affectedRows] = await UserWallet.update(
          updateFields,
          {
            where: whereCondition,
            transaction,
          }
        );

        if (affectedRows === 0) {
          console.warn(
            `[预约请求] 用户 ${userId} 余额不足或并发修改冲突`
          );
          await transaction.rollback();
          return res
            .status(400)
            .json(
              errorResponse(tFromRequest(req, "errors.insufficientBalanceForReservation"))
            );
        }
        console.log(`[预约请求] 用户 ${userId} 余额原子更新成功`);

        const results: Array<{
          sessionId: number;
          reservations: Array<{ roundIndex: number; roomId: string }>;
        }> = [];
        for (const { sessionRecord, rounds, tickets } of processedSessions) {
          const sessionReservations: Array<{
            roundIndex: number;
            roomId: string;
            ticketType: string;
          }> = [];
          for (let i = 0; i < rounds.length; i++) {
            const roundIndex = rounds[i];
            const ticketType = tickets[i];
            const roomData = await assignRoomForRound(
              sessionRecord.id,
              roundIndex,
              transaction
            );
            console.log(
              `[预约请求] 用户 ${userId} 分配到房间：sessionId=${sessionRecord.id}, roundIndex=${roundIndex}, roomId=${roomData.roomId}, roomNumber=${roomData.roomNumber}`
            );
            const existingRes = await Reservation.findOne({
              where: { userId, sessionId: sessionRecord.id, roundIndex },
              transaction,
            });
            if (existingRes) {
              throw new Error(
                `重复预约: sessionId ${sessionRecord.id}, round ${roundIndex}`
              );
            }
            const reservationCount = await Reservation.count({
              where: { roomId: roomData.room.id },
              transaction,
            });
            if (reservationCount >= GAME_CONFIG.MAX_PARTICIPANTS) {
              throw new Error(`房间 ${roomData.room.id} 人数已满`);
            }
            await sessionRecord.increment("reservedCount", {
              by: 1,
              transaction,
            });
            await Reservation.create(
              {
                userId: userId,
                roomNumber: roomData.roomNumber,
                walletId: walletId,
                sessionId: sessionRecord.id,
                roomId: roomData.room.id,
                roundIndex,
                status: "reserved",
                reservedAt: new Date(),
                ticketType, // 添加票类型字段
              },
              { transaction }
            );
            console.log(
              `[预约请求] 预约记录创建成功 - 用户 ${userId}, sessionId ${sessionRecord.id}, round ${roundIndex}, roomId ${roomData.room.id}`
            );
            await createWalletHistory({
              userId,
              walletId: walletId,
              amount: GAME_CONFIG.USD_COST,
              currency: "usd",
              sessionId: sessionRecord.id,
              session_category: sessionRecord.session_category,
              roundIndex,
              transaction,
            });
            await createWalletHistory({
              userId,
              walletId: walletId,
              amount: GAME_CONFIG.TICKET_COST,
              currency: ticketType, // 使用指定的票类型
              sessionId: sessionRecord.id,
              session_category: sessionRecord.session_category,
              roundIndex,
              transaction,
            });
            await createGameHistory({
              userId: userId,
              walletId: walletId,
              sessionId: sessionRecord.id,
              roundIndex,
              roomId: roomData.room.id,
              betAmount: GAME_CONFIG.USD_COST,
              ticketType, // 添加票类型字段
              transaction,
            });
            sessionReservations.push({
              roundIndex,
              roomId: roomData.room.id.toString(),
              ticketType,
            });
          }
          results.push({
            sessionId: sessionRecord.id,
            reservations: sessionReservations,
          });
        }
        await transaction.commit();
        console.log(
          `[预约请求] 事务提交成功 - userId: ${userId}, reservations:`,
          JSON.stringify(results)
        );
        return res.json(successResponse(
          {
            reservations: results
          },
          tFromRequest(req, "success.reservationSuccess")
        ));
      } catch (err) {
        await transaction.rollback();
        console.error(`[预约请求] 用户 ${userId} 事务处理错误:`, err);
        return res.status(500).json(errorResponse(tFromRequest(req, "errors.serverError"), err));
      }
    } catch (err) {
      console.error(`[预约请求] 用户 ${userId} 请求处理错误:`, err);
      return res.status(500).json(errorResponse(tFromRequest(req, "errors.serverError"), err));
    } finally {
      if (userLockToken) {
        await releaseLock(userLockKey, userLockToken);
      }
    }
  }
);

//@ts-ignore
router.get("/reservations", walletAuthMiddleware, async (req: Request, res: Response) => {
  const myReq = req as MyRequest;
  const { userId, walletId } = myReq.user!;

  try {
    // 查询当前用户的预约记录，并关联 Session 和 Room 表
    const reservations = await Reservation.findAll({
      where: { userId, walletId },
      include: [
        {
          model: Session,
          attributes: ["session_dt", "session_category"],
        },
        {
          model: Room,
          attributes: ["id", "sessionId", "roundIndex", "isFull"],
        },
      ],
      order: [["reservedAt", "DESC"]], // 按预约时间降序
    });

    console.log(reservations);
    
    // 格式化返回数据
    const formattedReservations = reservations.map((reservation) => ({
      reservationId: reservation.id,
      sessionId: reservation.sessionId,
      //@ts-ignore
      session_dt: reservation.Session?.session_dt,
      //@ts-ignore
      session_category: reservation.Session?.session_category,
      roundIndex: reservation.roundIndex,
      roomId: reservation.roomId,
      roomNumber: reservation.roomNumber,
      status: reservation.status,
      reservedAt: reservation.reservedAt,
    }));

    return res.json(successResponse(
      formattedReservations,
      tFromRequest(req, "success.getReservations")
    ));
  } catch (error) {
    console.error("Error fetching reservations:", error);
    return res.status(500).json(errorResponse(
      tFromRequest(req, "errors.serverError"),
      (error as Error).message
    ));
  }
});

export default router;