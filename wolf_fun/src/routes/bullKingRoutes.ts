// src/routes/bullKingRoutes.ts
import express from "express";
import { claimBullKingReward, claimMoofHoldersReward, claimPersonalKolReward, claimTeamKolReward, getBullKingLeaderboard, getMoofHoldersLeaderboard, getPersonalKolLeaderboard, getTeamKolLeaderboard } from "../services/bullKingService";
import { walletAuthMiddleware } from "../middlewares/walletAuth";
import { MyRequest } from "../types/customRequest";
import { ajv, tFromRequest, formatValidationErrors } from "../i18n";
import { languageMiddleware } from "../middlewares/languageMiddleware";
import { successResponse, errorResponse } from "../utils/responseUtil";

const router = express.Router();

// 应用语言中间件到所有路由
router.use(languageMiddleware);

// 定义查询参数验证模式
const leaderboardQuerySchema = {
  type: "object",
  properties: {
    limit: { type: "string", pattern: "^[0-9]+$" }
  }
};

const validateLeaderboardQuery = ajv.compile(leaderboardQuerySchema);

/**
 * @api {get} /api/bull-king/leaderboard 获取牛王宝座排行榜
 * @apiName GetBullKingLeaderboard
 * @apiGroup BullKing
 * @apiParam {Number} [limit=10] 限制返回的记录数量
 * @apiSuccess {Array} data 排行榜数据
 */
//@ts-ignore
router.get("/leaderboard", walletAuthMiddleware, async (req, res, next) => {
  try {
    // 验证查询参数
    const valid = validateLeaderboardQuery(req.query);
    if (!valid) {
      return res.status(400).json(errorResponse(
        tFromRequest(req, "errors.paramValidation"),
        formatValidationErrors(validateLeaderboardQuery.errors || [], req.language)
      ));
    }

    const myReq = req as MyRequest;
    const { walletId } = myReq.user!;
    const limit = req.query.limit ? parseInt(req.query.limit as string) : 100;
    
    if (isNaN(limit) || limit <= 0) {
      return res.status(400).json(errorResponse(
        tFromRequest(req, "errors.invalidLimit")
      ));
    }
    
    const leaderboard = await getBullKingLeaderboard(limit, walletId);
    
    res.json(successResponse(leaderboard));
  } catch (error) {
    console.error("获取牛王宝座排行榜失败:", error);
    res.status(500).json(errorResponse(
      tFromRequest(req, "errors.getBullKingLeaderboardFailed"),
      (error as Error).message
    ));
  }
});

// 其他路由也使用相同的验证模式
//@ts-ignore
router.get("/moof-holders", walletAuthMiddleware, async (req, res) => {
  try {
    const valid = validateLeaderboardQuery(req.query);
    if (!valid) {
      return res.status(400).json(errorResponse(
        tFromRequest(req, "errors.paramValidation"),
        formatValidationErrors(validateLeaderboardQuery.errors || [], req.language)
      ));
    }

    const myReq = req as MyRequest;
    const { walletId } = myReq.user!;
    const limit = req.query.limit ? parseInt(req.query.limit as string) : 100;
    
    if (isNaN(limit) || limit <= 0) {
      return res.status(400).json(errorResponse(
        tFromRequest(req, "errors.invalidLimit")
      ));
    }
    
    const result = await getMoofHoldersLeaderboard(limit, walletId);
    
    res.json(successResponse(result));
  } catch (error) {
    console.error("获取MOOF持有者排行榜失败:", error);
    res.status(500).json(errorResponse(
      tFromRequest(req, "errors.getMoofHoldersLeaderboardFailed"),
      (error as Error).message
    ));
  }
});

/**
 * @api {post} /api/bull-king/moof-holders/claim 领取MOOF持有者奖励
 * @apiName ClaimMoofHoldersReward
 * @apiGroup BullKing
 * @apiSuccess {Boolean} success 是否成功
 * @apiSuccess {Object} data 领取结果
 */
router.post("/moof-holders/claim", walletAuthMiddleware, async (req, res) => {
  try {
    const myReq = req as MyRequest;
    const { walletId } = myReq.user!;
    if (!walletId) {
      throw new Error(tFromRequest(req, "errors.walletNotFound"));
    }

    const result = await claimMoofHoldersReward(walletId);
    
    res.json(successResponse(
      result,
      tFromRequest(req, "success.claimMoofHoldersReward")
    ));
  } catch (error) {
    console.error("领取MOOF持有者奖励失败:", error);
    res.status(500).json(errorResponse(
      tFromRequest(req, "errors.claimMoofHoldersRewardFailed"),
      (error as Error).message
    ));
  }
});

/**
 * @api {post} /api/bull-king/claim 领取牛王宝座奖励
 * @apiName ClaimBullKingReward
 * @apiGroup BullKing
 * @apiSuccess {Boolean} success 是否成功
 * @apiSuccess {Object} data 领取结果
 */
router.post("/claim", walletAuthMiddleware, async (req, res) => {
  try {
    const myReq = req as MyRequest;
    const { walletId } = myReq.user!;
    if (!walletId) {
      throw new Error(tFromRequest(req, "errors.walletNotFound"));
    }

    const result = await claimBullKingReward(walletId);
    
    res.json(successResponse(
      result,
      tFromRequest(req, "success.claimBullKingReward")
    ));
  } catch (error) {
    console.error("领取牛王宝座奖励失败:", error);
    res.status(500).json(errorResponse(
      tFromRequest(req, "errors.claimBullKingRewardFailed"),
      (error as Error).message
    ));
  }
});

/**
 * @api {get} /api/bull-king/personal-kol 获取个人KOL排行榜
 * @apiName GetPersonalKolLeaderboard
 * @apiGroup BullKing
 * @apiParam {Number} [limit=100] 限制返回的记录数量
 * @apiSuccess {Array} data.leaderboard 排行榜数据
 * @apiSuccess {Object} data.poolInfo 奖金池信息
 * @apiSuccess {Object} data.userInfo 用户个人信息
 */
//@ts-ignore
router.get("/personal-kol", walletAuthMiddleware, async (req, res) => {
  try {
    const valid = validateLeaderboardQuery(req.query);
    if (!valid) {
      return res.status(400).json(errorResponse(
        tFromRequest(req, "errors.paramValidation"),
        formatValidationErrors(validateLeaderboardQuery.errors || [], req.language)
      ));
    }

    const myReq = req as MyRequest;
    const { walletId } = myReq.user!;
    const limit = req.query.limit ? parseInt(req.query.limit as string) : 100;
    
    if (isNaN(limit) || limit <= 0) {
      return res.status(400).json(errorResponse(
        tFromRequest(req, "errors.invalidLimit")
      ));
    }
    
    const result = await getPersonalKolLeaderboard(limit, walletId);
    
    res.json(successResponse(result));
  } catch (error) {
    console.error("获取个人KOL排行榜失败:", error);
    res.status(500).json(errorResponse(
      tFromRequest(req, "errors.getPersonalKolLeaderboardFailed"),
      (error as Error).message
    ));
  }
});

/**
 * @api {post} /api/bull-king/personal-kol/claim 领取个人KOL奖励
 * @apiName ClaimPersonalKolReward
 * @apiGroup BullKing
 * @apiSuccess {Boolean} success 是否成功
 * @apiSuccess {Object} data 领取结果
 */
router.post("/personal-kol/claim", walletAuthMiddleware, async (req, res) => {
  try {
    const myReq = req as MyRequest;
    const { walletId } = myReq.user!;
    if (!walletId) {
      throw new Error(tFromRequest(req, "errors.walletNotFound"));
    }

    const result = await claimPersonalKolReward(walletId);
    
    res.json(successResponse(
      result,
      tFromRequest(req, "success.claimPersonalKolReward")
    ));
  } catch (error) {
    console.error("领取个人KOL奖励失败:", error);
    res.status(500).json(errorResponse(
      tFromRequest(req, "errors.claimPersonalKolRewardFailed"),
      (error as Error).message
    ));
  }
});

/**
 * @api {get} /api/bull-king/team-kol 获取团队KOL排行榜
 * @apiName GetTeamKolLeaderboard
 * @apiGroup BullKing
 * @apiParam {Number} [limit=100] 限制返回的记录数量
 * @apiSuccess {Array} data.leaderboard 排行榜数据
 * @apiSuccess {Object} data.poolInfo 奖金池信息
 * @apiSuccess {Object} data.userInfo 用户个人信息
 */
//@ts-ignore
router.get("/team-kol", walletAuthMiddleware, async (req, res) => {
  try {
    const valid = validateLeaderboardQuery(req.query);
    if (!valid) {
      return res.status(400).json(errorResponse(
        tFromRequest(req, "errors.paramValidation"),
        formatValidationErrors(validateLeaderboardQuery.errors || [], req.language)
      ));
    }

    const myReq = req as MyRequest;
    const { walletId } = myReq.user!;
    const limit = req.query.limit ? parseInt(req.query.limit as string) : 100;
    
    if (isNaN(limit) || limit <= 0) {
      return res.status(400).json(errorResponse(
        tFromRequest(req, "errors.invalidLimit")
      ));
    }
    
    const result = await getTeamKolLeaderboard(limit, walletId);
    
    res.json(successResponse(result));
  } catch (error) {
    console.error("获取团队KOL排行榜失败:", error);
    res.status(500).json(errorResponse(
      tFromRequest(req, "errors.getTeamKolLeaderboardFailed"),
      (error as Error).message
    ));
  }
});

/**
 * @api {post} /api/bull-king/team-kol/claim 领取团队KOL奖励
 * @apiName ClaimTeamKolReward
 * @apiGroup BullKing
 * @apiSuccess {Boolean} success 是否成功
 * @apiSuccess {Object} data 领取结果
 */
router.post("/team-kol/claim", walletAuthMiddleware, async (req, res) => {
  try {
    const myReq = req as MyRequest;
    const { walletId } = myReq.user!;
    if (!walletId) {
      throw new Error(tFromRequest(req, "errors.walletNotFound"));
    }

    const result = await claimTeamKolReward(walletId);
    
    res.json(successResponse(
      result,
      tFromRequest(req, "success.claimTeamKolReward")
    ));
  } catch (error) {
    console.error("领取团队KOL奖励失败:", error);
    res.status(500).json(errorResponse(
      tFromRequest(req, "errors.claimTeamKolRewardFailed"),
      (error as Error).message
    ));
  }
});

export default router;