import express, { Request, Response, NextFunction } from 'express';
import testResetController from '../controllers/testResetController';
import { walletAuthMiddleware } from '../middlewares/walletAuth';
import { languageMiddleware } from '../middlewares/languageMiddleware';
import { errorResponse } from '../utils/responseUtil';
import { tFromRequest } from '../i18n';
import { logger } from '../utils/logger';
import rateLimit from 'express-rate-limit';

const router = express.Router();

// 移除了开发环境检查中间件 - 测试重置功能现在在所有环境下都可用

/**
 * 安全警告中间件
 * 记录所有对测试重置API的访问
 */
const securityWarningMiddleware = (req: Request, res: Response, next: NextFunction): void => {
  logger.warn('测试重置API访问记录', {
    path: req.path,
    method: req.method,
    ip: req.ip,
    userAgent: req.headers['user-agent'],
    timestamp: new Date().toISOString(),
    headers: {
      authorization: req.headers.authorization ? '***' : undefined,
      'content-type': req.headers['content-type']
    }
  });

  next();
};

/**
 * 速率限制中间件
 * 防止重置操作被频繁调用
 */
const resetRateLimit = rateLimit({
  windowMs: 60 * 1000, // 1分钟
  max: 5, // 每分钟最多5次重置操作
  message: {
    ok: false,
    message: '重置操作过于频繁，请稍后再试'
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req, res) => {
    logger.warn('重置API速率限制触发', {
      ip: req.ip,
      path: req.path,
      userAgent: req.headers['user-agent']
    });
    
    res.status(429).json(errorResponse(
      tFromRequest(req, 'errors.rateLimitExceeded') || '操作过于频繁，请稍后再试'
    ));
  }
});

// 移除了确认操作中间件 - 不再需要 X-Confirm-Reset 请求头

// 应用全局中间件
router.use(languageMiddleware);
router.use(securityWarningMiddleware);

// 健康检查接口（无需身份验证）
router.get('/health', testResetController.healthCheck);

// 以下路由需要身份验证
router.use(walletAuthMiddleware);

// 获取重置安全检查信息
router.get('/reset-safety-info', testResetController.getResetSafetyInfo);

// 重置游戏状态（需要速率限制）
router.post('/reset-game-state',
  resetRateLimit,
  testResetController.resetGameState
);

// 错误处理中间件
router.use((error: Error, req: Request, res: Response, next: NextFunction) => {
  logger.error('测试重置路由错误', {
    path: req.path,
    method: req.method,
    error: {
      message: error.message,
      stack: error.stack
    }
  });
  
  res.status(500).json(errorResponse(
    tFromRequest(req, 'errors.serverError') || '服务器内部错误'
  ));
});

export default router;
