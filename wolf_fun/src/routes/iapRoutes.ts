import { Router } from 'express';
import iapController from '../controllers/iapController';
import { walletAuthMiddleware } from '../middlewares/walletAuth';
import { languageMiddleware } from "../middlewares/languageMiddleware";

const router = Router();

router.use(languageMiddleware);
// 所有路由都需要用户认证
router.use(walletAuthMiddleware);

// 获取商店商品列表
// @ts-ignore
router.get('/store/products', iapController.getStoreProducts);

// 创建支付订单
// @ts-ignore
router.post('/payment/create', iapController.createPayment);

// 获取用户拥有的道具
// @ts-ignore
router.get('/boosters', iapController.getUserBoosters);

// 使用道具
// @ts-ignore
router.post('/boosters/use', iapController.useBooster);

// 获取激活的道具
// @ts-ignore
router.get('/boosters/active', iapController.getActiveBoosters);

// 预览时间跳跃收益
// @ts-ignore
router.get('/time-warp/preview', iapController.previewTimeWarpRewards);

// 获取VIP状态
// @ts-ignore
router.get('/vip/status', iapController.getVipStatus);

// 获取购买历史
// @ts-ignore
router.get('/purchase/history', iapController.getPurchaseHistory);

export default router;