// /src/routes/my_reservations.ts
import { Router, Request, Response } from "express";
import { Reservation, Session, Round } from "../models";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import { Op } from "sequelize";
import { walletAuthMiddleware } from "../middlewares/walletAuth";
import { MyRequest } from "../types/customRequest";
import { ajv, tFromRequest, formatValidationErrors } from "../i18n";
import { languageMiddleware } from "../middlewares/languageMiddleware";
import { successResponse, errorResponse } from "../utils/responseUtil";

dayjs.extend(utc);
dayjs.extend(timezone);

const router = Router();
const TZ = "Asia/Shanghai";

// 应用语言中间件到所有路由
router.use(languageMiddleware);

// 定义我的预约查询参数验证模式
const myReservationsSchema = {
  type: "object",
  properties: {},
  additionalProperties: false
};

const validateMyReservations = ajv.compile(myReservationsSchema);
//@ts-ignore
router.get("/my_reservations", walletAuthMiddleware, async (req: Request, res: Response) => {
  try {
    const myReq = req as MyRequest;
    const userId = myReq.user!.userId;
    const walletId = myReq.user!.walletId;

    // 验证请求参数
    const valid = validateMyReservations(req.query);
    if (!valid) {
      return res.status(400).json(errorResponse(
        tFromRequest(req, "errors.paramValidation"),
        formatValidationErrors(validateMyReservations.errors || [], req.language)
      ));
    }

    if (!userId || !walletId) {
      return res.status(400).json(errorResponse(tFromRequest(req, "errors.missingUserOrWalletId")));
    }

    // 使用中国时区获取当天的起始和结束时间
    const startOfToday = dayjs().tz(TZ).startOf("day").toDate();
    const endOfToday = dayjs().tz(TZ).endOf("day").toDate();

    // 查询当前用户当天的所有预约记录
    const reservations = await Reservation.findAll({
      where: {
        userId,
        walletId,
        reservedAt: {
          [Op.gte]: startOfToday,
          [Op.lte]: endOfToday,
        },
      },
      include: [
        {
          model: Session,
          attributes: ["session_dt", "session_category"],
        },
        {
          model: Round,
          attributes: ["result_time"],
          required: false,
          where: {
            sessionId: { [Op.col]: 'Reservation.sessionId' },
            roundIndex: { [Op.col]: 'Reservation.roundIndex' }
          }
        }
      ],
    });

    res.json(successResponse(
      { reservations },
      tFromRequest(req, "success.getMyReservations")
    ));
  } catch (error) {
    console.error("查询当天预约记录错误：", error);
    res.status(500).json(errorResponse(
      tFromRequest(req, "errors.serverError"),
      (error as Error).message
    ));
  }
});

export default router;