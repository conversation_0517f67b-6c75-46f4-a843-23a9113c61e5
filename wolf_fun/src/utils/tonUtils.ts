// src/utils/tonUtils.ts

import { Address } from "@ton/core";

/**
 * 将地址转换成可读格式 (类似 tonconnect/ui-react 的 toUserFriendlyAddress)
 * @param address 原始Ton地址(比如 'EQBL...or kQBL...' 或 '0:xxxx')
 * @param isTestnet 是否测试网
 * @returns 字符串，如 'EQBL...'
 */
export function toUserFriendlyAddress(
  address: string,
  isTestnet = false
): string {
  const parsed = Address.parse(address);

  // toString 的选项可自行调配：
  //  - urlSafe: 是否url安全
  //  - bounceable: 是否bounceable
  //  - testOnly: 是否测试网
  //
  return parsed.toString({
    urlSafe: false,
    bounceable: true,
    testOnly: isTestnet,
  });
}
