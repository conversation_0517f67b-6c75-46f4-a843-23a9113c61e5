// BigNumber.js 配置文件
// 用于农场区和出货线系统的高精度数值计算

import BigNumber from 'bignumber.js';
import {
  getFarmPlotBarnCount,
  getFarmPlotProductionSpeed,
  getFarmPlotUnlockCost,
  getFarmPlotMilkProduction,
  getFarmPlotUpgradeCost,
  FALLBACK_FARM_PLOT_BARN_COUNT,
  FALLBACK_FARM_PLOT_PRODUCTION_SPEED,
  FALLBACK_FARM_PLOT_MILK_PRODUCTION
} from '../config/farmPlotConfig';

// 配置 BigNumber 全局设置
BigNumber.config({
  // 设置小数位数为3位
  DECIMAL_PLACES: 3,
  // 设置舍入模式为四舍五入
  ROUNDING_MODE: BigNumber.ROUND_HALF_UP,
  // 设置指数表示法的范围
  EXPONENTIAL_AT: [-7, 20],
  // 设置最大有效数字
  RANGE: [-1e9, 1e9],
  // 启用加密安全的随机数生成
  CRYPTO: false,
  // 设置模运算的精度
  MODULO_MODE: BigNumber.ROUND_DOWN,
  // 设置除零时的行为
  POW_PRECISION: 0,
  // 设置格式化选项
  FORMAT: {
    prefix: '',
    suffix: '',
    decimalSeparator: '.',
    groupSeparator: ',',
    groupSize: 3,
    secondaryGroupSize: 0,
    fractionGroupSeparator: ' ',
    fractionGroupSize: 0
  }
});

/**
 * 创建 BigNumber 实例的工厂函数
 * @param value 数值
 * @returns BigNumber 实例
 */
export function createBigNumber(value: number | string | BigNumber): BigNumber {
  return new BigNumber(value);
}

/**
 * 格式化数值为3位小数的字符串
 * @param value BigNumber 或数值
 * @returns 格式化后的字符串
 */
export function formatToThreeDecimals(value: number | string | BigNumber): string {
  const bn = value instanceof BigNumber ? value : new BigNumber(value);
  return bn.toFixed(3);
}

/**
 * 格式化数值为3位小数的数字
 * @param value BigNumber 或数值
 * @returns 格式化后的数字
 */
export function formatToThreeDecimalsNumber(value: number | string | BigNumber): number {
  const bn = value instanceof BigNumber ? value : new BigNumber(value);
  return parseFloat(bn.toFixed(3));
}

/**
 * 安全的幂运算
 * @param base 底数
 * @param exponent 指数
 * @returns BigNumber 结果
 */
export function safePow(base: number | string | BigNumber, exponent: number | string | BigNumber): BigNumber {
  const baseBN = base instanceof BigNumber ? base : new BigNumber(base);
  const expBN = exponent instanceof BigNumber ? exponent : new BigNumber(exponent);
  return baseBN.pow(expBN);
}

/**
 * 安全的乘法运算
 * @param a 乘数1
 * @param b 乘数2
 * @returns BigNumber 结果
 */
export function safeMultiply(a: number | string | BigNumber, b: number | string | BigNumber): BigNumber {
  const aBN = a instanceof BigNumber ? a : new BigNumber(a);
  const bBN = b instanceof BigNumber ? b : new BigNumber(b);
  return aBN.multipliedBy(bBN);
}

/**
 * 安全的除法运算
 * @param a 被除数
 * @param b 除数
 * @returns BigNumber 结果
 */
export function safeDivide(a: number | string | BigNumber, b: number | string | BigNumber): BigNumber {
  const aBN = a instanceof BigNumber ? a : new BigNumber(a);
  const bBN = b instanceof BigNumber ? b : new BigNumber(b);
  return aBN.dividedBy(bBN);
}

/**
 * 安全的加法运算
 * @param a 加数1
 * @param b 加数2
 * @returns BigNumber 结果
 */
export function safeAdd(a: number | string | BigNumber, b: number | string | BigNumber): BigNumber {
  const aBN = a instanceof BigNumber ? a : new BigNumber(a);
  const bBN = b instanceof BigNumber ? b : new BigNumber(b);
  return aBN.plus(bBN);
}

/**
 * 安全的减法运算
 * @param a 被减数
 * @param b 减数
 * @returns BigNumber 结果
 */
export function safeSubtract(a: number | string | BigNumber, b: number | string | BigNumber): BigNumber {
  const aBN = a instanceof BigNumber ? a : new BigNumber(a);
  const bBN = b instanceof BigNumber ? b : new BigNumber(b);
  return aBN.minus(bBN);
}

/**
 * 农场区专用计算函数
 */
export class FarmPlotCalculator {
  /**
   * 计算基础产量：使用动态配置查表
   * @param level 等级 (0-50，兼容旧版本1-20)
   * @param plotNumber 牧场区编号 (1-20)
   * @returns 基础产量
   */
  static async calculateBaseProduction(level: number, plotNumber: number = 1): Promise<number> {
    return await getFarmPlotMilkProduction(plotNumber, level);
  }

  /**
   * 计算生产速度：使用动态配置查表
   * @param level 当前等级 (0-50，兼容旧版本1-20)
   * @returns 生产速度（秒/次）
   */
  static async calculateProductionSpeed(level: number): Promise<number> {
    return await getFarmPlotProductionSpeed(level);
  }

  /**
   * 计算升级后的生产速度：使用动态配置查表
   * @param currentLevel 当前等级 (0-49，兼容旧版本1-19)
   * @returns 升级后的生产速度（秒/次）
   */
  static async calculateUpgradedSpeed(currentLevel: number): Promise<number> {
    if (currentLevel >= 50) {
      throw new Error('已达到最高等级，无法升级');
    }
    return await this.calculateProductionSpeed(currentLevel + 1);
  }

  /**
   * 计算升级费用：使用动态配置查表
   * @param plotNumber 农场区块编号 (1-20)
   * @param level 当前等级 (0-50，兼容旧版本1-20)
   * @returns 升级费用
   */
  static async calculateUpgradeCostByLevel(plotNumber: number, level: number): Promise<number> {
    return await getFarmPlotUpgradeCost(plotNumber, level);
  }

  /**
   * 计算升级后的费用：使用动态配置查表
   * @param plotNumber 农场区块编号 (1-20)
   * @param currentLevel 当前等级 (0-49，兼容旧版本1-19)
   * @returns 升级后的费用
   */
  static async calculateUpgradeCost(plotNumber: number, currentLevel: number): Promise<number> {
    if (currentLevel >= 50) {
      throw new Error('已达到最高等级，无法升级');
    }
    return await this.calculateUpgradeCostByLevel(plotNumber, currentLevel + 1);
  }

  /**
   * 计算解锁费用：使用预设数组查表（解锁费用仍使用硬编码配置）
   * @param plotNumber 农场区编号 (1-20)
   * @returns 解锁费用
   */
  static calculateUnlockCost(plotNumber: number): number {
    return getFarmPlotUnlockCost(plotNumber);
  }

  /**
   * 计算升级后的产量：使用动态配置查表
   * @param plotNumber 农场区块编号 (1-20)
   * @param currentLevel 当前等级 (0-49，兼容旧版本1-19)
   * @returns 升级后的产量
   */
  static async calculateUpgradedProduction(plotNumber: number, currentLevel: number): Promise<number> {
    if (currentLevel >= 50) {
      throw new Error('已达到最高等级，无法升级');
    }
    return await this.calculateBaseProduction(currentLevel + 1, plotNumber);
  }

  /**
   * 计算牛舍数量：使用动态配置查表
   * @param level 等级 (0-50，兼容旧版本1-20)
   * @returns 牛舍数量
   */
  static async calculateBarnCount(level: number): Promise<number> {
    return await getFarmPlotBarnCount(level);
  }

  /**
   * 同步版本的计算函数（用于兼容现有代码）
   * 注意：这些函数使用硬编码的降级配置，仅用于兼容现有代码
   */
  static calculateBaseProductionSync(level: number, plotNumber: number = 1): number {
    // 兼容旧版本：如果传入1-20，转换为0-19
    const actualLevel = level >= 1 && level <= 20 ? level - 1 : level;

    if (plotNumber >= 1 && plotNumber <= 20 &&
        actualLevel < FALLBACK_FARM_PLOT_MILK_PRODUCTION[plotNumber - 1]?.length) {
      return FALLBACK_FARM_PLOT_MILK_PRODUCTION[plotNumber - 1][actualLevel];
    }

    return plotNumber * (actualLevel + 1) * 0.5;
  }

  static calculateProductionSpeedSync(level: number): number {
    // 兼容旧版本：如果传入1-20，转换为0-19
    const actualLevel = level >= 1 && level <= 20 ? level - 1 : level;

    if (actualLevel < FALLBACK_FARM_PLOT_PRODUCTION_SPEED.length) {
      return FALLBACK_FARM_PLOT_PRODUCTION_SPEED[actualLevel];
    }

    return Math.max(1, 5 - actualLevel * 0.1);
  }

  static calculateBarnCountSync(level: number): number {
    // 兼容旧版本：如果传入1-20，转换为0-19
    const actualLevel = level >= 1 && level <= 20 ? level - 1 : level;

    if (actualLevel < FALLBACK_FARM_PLOT_BARN_COUNT.length) {
      return FALLBACK_FARM_PLOT_BARN_COUNT[actualLevel];
    }

    return Math.min(20, actualLevel + 1);
  }
}

/**
 * 出货线专用计算函数
 */
export class DeliveryLineCalculator {
  /**
   * 计算出货速度：当前速度 ÷ 1.01
   * @param currentSpeed 当前速度
   * @returns 新的出货速度
   */
  static calculateUpgradedSpeed(currentSpeed: number): number {
    const current = createBigNumber(currentSpeed);
    const divisor = createBigNumber(1.01);
    const result = current.dividedBy(divisor);
    return formatToThreeDecimalsNumber(result);
  }

  /**
   * 计算方块容量：当前容量 × 2.0
   * @param currentUnit 当前容量
   * @returns 新的方块容量
   */
  static calculateUpgradedUnit(currentUnit: number): number {
    const current = createBigNumber(currentUnit);
    const multiplier = createBigNumber(2.0);
    const result = current.multipliedBy(multiplier);
    return formatToThreeDecimalsNumber(result);
  }

  /**
   * 计算方块价格：当前价格 × 2.0
   * @param currentPrice 当前价格
   * @returns 新的方块价格
   */
  static calculateUpgradedPrice(currentPrice: number): number {
    const current = createBigNumber(currentPrice);
    const multiplier = createBigNumber(2.0);
    const result = current.multipliedBy(multiplier);
    return formatToThreeDecimalsNumber(result);
  }

  /**
   * 计算升级费用：当前费用 × 2.0
   * @param currentCost 当前费用
   * @returns 新的升级费用
   */
  static calculateUpgradeCost(currentCost: number): number {
    const current = createBigNumber(currentCost);
    const multiplier = createBigNumber(2.0);
    const result = current.multipliedBy(multiplier);
    return formatToThreeDecimalsNumber(result);
  }
}

export default BigNumber;
