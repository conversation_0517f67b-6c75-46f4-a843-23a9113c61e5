// 严格验证批量资源更新的专用日志记录器
// 提供详细的监控、分析和调试信息

import dayjs from 'dayjs';

/**
 * 验证结果统计接口
 */
export interface ValidationStats {
  totalRequests: number;
  validationPassed: number;
  validationFailed: number;
  fallbackUsed: number;
  timeWindowRejected: number;
  averageProcessingTime: number;
  errorCount: number;
}

/**
 * 请求日志接口
 */
export interface RequestLog {
  userId: number;
  walletId: number;
  timestamp: string;
  request: any;
  response: any;
  processingTime: number;
  validationResult: {
    passed: boolean;
    usedStrictValidation: boolean;
    fallbackToOldMethod: boolean;
    timeWindowValid?: boolean;
    reason?: string;
  };
  resourceChanges: {
    gem: number;
    milkIncreased: number;
    milkDecreased: number;
  };
}

/**
 * 严格验证日志记录器类
 */
export class StrictValidationLogger {
  private static logs: RequestLog[] = [];
  private static stats: ValidationStats = {
    totalRequests: 0,
    validationPassed: 0,
    validationFailed: 0,
    fallbackUsed: 0,
    timeWindowRejected: 0,
    averageProcessingTime: 0,
    errorCount: 0
  };

  /**
   * 记录请求日志
   */
  static logRequest(log: RequestLog): void {
    // 添加到日志数组
    this.logs.push(log);
    
    // 更新统计信息
    this.updateStats(log);
    
    // 保持日志数组大小在合理范围内（最多保留1000条）
    if (this.logs.length > 1000) {
      this.logs = this.logs.slice(-1000);
    }
    
    // 输出详细日志
    this.outputDetailedLog(log);
  }

  /**
   * 更新统计信息
   */
  private static updateStats(log: RequestLog): void {
    this.stats.totalRequests++;
    
    if (log.validationResult.passed) {
      this.stats.validationPassed++;
    } else {
      this.stats.validationFailed++;
    }
    
    if (log.validationResult.fallbackToOldMethod) {
      this.stats.fallbackUsed++;
    }
    
    if (log.validationResult.timeWindowValid === false) {
      this.stats.timeWindowRejected++;
    }
    
    // 更新平均处理时间
    const totalTime = this.stats.averageProcessingTime * (this.stats.totalRequests - 1) + log.processingTime;
    this.stats.averageProcessingTime = totalTime / this.stats.totalRequests;
  }

  /**
   * 输出详细日志
   */
  private static outputDetailedLog(log: RequestLog): void {
    const logLevel = log.validationResult.passed ? 'INFO' : 'WARN';
    const status = log.validationResult.passed ? '✅' : '❌';
    
    console.log(`[严格验证日志] ${status} ${logLevel} - 用户${log.userId} - ${log.timestamp}`);
    console.log(`  处理时间: ${log.processingTime}ms`);
    console.log(`  验证结果: ${log.validationResult.passed ? '通过' : '失败'}`);
    console.log(`  使用严格验证: ${log.validationResult.usedStrictValidation ? '是' : '否'}`);
    console.log(`  回退到旧方法: ${log.validationResult.fallbackToOldMethod ? '是' : '否'}`);
    
    if (log.validationResult.timeWindowValid === false) {
      console.log(`  时间窗口: 无效 - ${log.validationResult.reason}`);
    }
    
    console.log(`  资源变化: GEM+${log.resourceChanges.gem}, 牛奶+${log.resourceChanges.milkIncreased}-${log.resourceChanges.milkDecreased}`);
    
    // 如果处理时间过长，输出警告
    if (log.processingTime > 500) {
      console.warn(`  ⚠️  处理时间过长: ${log.processingTime}ms`);
    }
  }

  /**
   * 记录错误
   */
  static logError(userId: number, walletId: number, error: any, processingTime: number): void {
    this.stats.errorCount++;
    
    console.error(`[严格验证错误] ❌ 用户${userId} - ${dayjs().format('YYYY-MM-DD HH:mm:ss')}`);
    console.error(`  处理时间: ${processingTime}ms`);
    console.error(`  错误详情:`, error);
  }

  /**
   * 获取统计信息
   */
  static getStats(): ValidationStats {
    return { ...this.stats };
  }

  /**
   * 获取最近的日志
   */
  static getRecentLogs(limit: number = 50): RequestLog[] {
    return this.logs.slice(-limit);
  }

  /**
   * 获取验证失败的日志
   */
  static getFailedValidationLogs(limit: number = 20): RequestLog[] {
    return this.logs
      .filter(log => !log.validationResult.passed)
      .slice(-limit);
  }

  /**
   * 获取回退到旧方法的日志
   */
  static getFallbackLogs(limit: number = 20): RequestLog[] {
    return this.logs
      .filter(log => log.validationResult.fallbackToOldMethod)
      .slice(-limit);
  }

  /**
   * 获取时间窗口问题的日志
   */
  static getTimeWindowRejectedLogs(limit: number = 20): RequestLog[] {
    return this.logs
      .filter(log => log.validationResult.timeWindowValid === false)
      .slice(-limit);
  }

  /**
   * 输出统计报告
   */
  static printStatsReport(): void {
    console.log('\n' + '='.repeat(60));
    console.log('📊 严格验证批量资源更新统计报告');
    console.log('='.repeat(60));
    console.log(`总请求数: ${this.stats.totalRequests}`);
    console.log(`验证通过: ${this.stats.validationPassed} (${(this.stats.validationPassed / this.stats.totalRequests * 100).toFixed(1)}%)`);
    console.log(`验证失败: ${this.stats.validationFailed} (${(this.stats.validationFailed / this.stats.totalRequests * 100).toFixed(1)}%)`);
    console.log(`回退使用: ${this.stats.fallbackUsed} (${(this.stats.fallbackUsed / this.stats.totalRequests * 100).toFixed(1)}%)`);
    console.log(`时间窗口拒绝: ${this.stats.timeWindowRejected} (${(this.stats.timeWindowRejected / this.stats.totalRequests * 100).toFixed(1)}%)`);
    console.log(`平均处理时间: ${this.stats.averageProcessingTime.toFixed(2)}ms`);
    console.log(`错误次数: ${this.stats.errorCount}`);
    console.log('='.repeat(60));
  }

  /**
   * 重置统计信息
   */
  static resetStats(): void {
    this.stats = {
      totalRequests: 0,
      validationPassed: 0,
      validationFailed: 0,
      fallbackUsed: 0,
      timeWindowRejected: 0,
      averageProcessingTime: 0,
      errorCount: 0
    };
    this.logs = [];
    console.log('[严格验证日志] 统计信息已重置');
  }

  /**
   * 分析验证失败的原因
   */
  static analyzeFailureReasons(): Record<string, number> {
    const reasons: Record<string, number> = {};
    
    this.logs
      .filter(log => !log.validationResult.passed)
      .forEach(log => {
        const reason = log.validationResult.reason || '未知原因';
        reasons[reason] = (reasons[reason] || 0) + 1;
      });
    
    return reasons;
  }

  /**
   * 获取性能分析报告
   */
  static getPerformanceAnalysis(): {
    fastRequests: number;
    normalRequests: number;
    slowRequests: number;
    averageTime: number;
    maxTime: number;
    minTime: number;
  } {
    if (this.logs.length === 0) {
      return {
        fastRequests: 0,
        normalRequests: 0,
        slowRequests: 0,
        averageTime: 0,
        maxTime: 0,
        minTime: 0
      };
    }
    
    const times = this.logs.map(log => log.processingTime);
    const fastRequests = times.filter(t => t < 100).length;
    const normalRequests = times.filter(t => t >= 100 && t < 500).length;
    const slowRequests = times.filter(t => t >= 500).length;
    
    return {
      fastRequests,
      normalRequests,
      slowRequests,
      averageTime: this.stats.averageProcessingTime,
      maxTime: Math.max(...times),
      minTime: Math.min(...times)
    };
  }
}
