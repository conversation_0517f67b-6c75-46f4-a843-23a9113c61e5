import { Response } from 'express';
import { MyRequest } from '../types/customRequest';
import gameLoopService from '../services/gameLoopService';
import { responseUtil } from '../utils/responseUtil';

class GameLoopController {
  // 获取用户的游戏状态
  public async getUserGameState(req: MyRequest, res: Response): Promise<void> {
    try {
      const walletId = Number(req.user?.walletId);
      
      if (!walletId) {
        responseUtil.error(res, '用户未登录', 401);
        return;
      }
      
      const gameState = await gameLoopService.getUserGameState(walletId);
      responseUtil.success(res, { gameState });
    } catch (error: any) {
      responseUtil.error(res, error.message);
    }
  }
  
  // 处理游戏循环的一次迭代（实时更新）
  public async processGameLoop(req: MyRequest, res: Response): Promise<void> {
    try {
      const walletId = Number(req.user?.walletId);
      
      if (!walletId) {
        responseUtil.error(res, '用户未登录', 401);
        return;
      }
      
      const result = await gameLoopService.processGameLoop(walletId);
      responseUtil.success(res, { result });
    } catch (error: any) {
      responseUtil.error(res, error.message);
    }
  }
  
  // 计算离线收益
  public async calculateOfflineEarnings(req: MyRequest, res: Response): Promise<void> {
    try {
      const walletId = Number(req.user?.walletId);
      const { lastOnlineTime } = req.body;
      
      if (!walletId) {
        responseUtil.error(res, '用户未登录', 401);
        return;
      }
      
      if (!lastOnlineTime) {
        responseUtil.error(res, '缺少上次在线时间');
        return;
      }
      
      const offlineEarnings = await gameLoopService.calculateOfflineEarnings(
        walletId,
        new Date(lastOnlineTime)
      );
      
      responseUtil.success(res, { offlineEarnings });
    } catch (error: any) {
      responseUtil.error(res, error.message);
    }
  }
  
  // 获取排行榜
  public async getLeaderboard(req: MyRequest, res: Response): Promise<void> {
    try {
      const limit = Number(req.query.limit) || 100;
      const leaderboard = await gameLoopService.getLeaderboard(limit);
      responseUtil.success(res, { leaderboard });
    } catch (error: any) {
      responseUtil.error(res, error.message);
    }
  }
  
  // 收集用户行为数据
  public async collectUserBehaviorData(req: MyRequest, res: Response): Promise<void> {
    try {
      const walletId = Number(req.user?.walletId);
      const { actionType, actionData } = req.body;
      
      if (!walletId) {
        responseUtil.error(res, '用户未登录', 401);
        return;
      }
      
      if (!actionType) {
        responseUtil.error(res, '缺少行为类型');
        return;
      }
      
      await gameLoopService.collectUserBehaviorData(walletId, actionType, actionData);
      responseUtil.success(res, { message: '数据收集成功' });
    } catch (error: any) {
      responseUtil.error(res, error.message);
    }
  }
}

export default new GameLoopController();