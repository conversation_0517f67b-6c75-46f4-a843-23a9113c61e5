// src/i18n/requestContext.ts
import { AsyncLocalStorage } from 'async_hooks';
import { SupportedLanguage } from '.';

// 创建请求上下文存储
export const requestContextStorage = new AsyncLocalStorage<{
  language: SupportedLanguage;
}>();

/**
 * 获取当前请求的语言
 * 如果不在请求上下文中，返回默认语言
 */
export function getCurrentRequestLanguage(): SupportedLanguage {
  const store = requestContextStorage.getStore();
  return store?.language || 'en';
}

/**
 * 设置当前请求的语言
 */
export function setCurrentRequestLanguage(language: SupportedLanguage): void {
  const store = requestContextStorage.getStore();
  if (store) {
    store.language = language;
  }
}