// src/middlewares/languageMiddleware.ts
import { Request, Response, NextFunction } from "express";
import { SUPPORTED_LANGUAGES, SupportedLanguage } from "../i18n";
import { requestContextStorage } from "../i18n/requestContext";

// 扩展Express的Request类型
declare global {
  namespace Express {
    interface Request {
      language: SupportedLanguage;
    }
  }
}

/**
 * 解析Accept-Language请求头并设置相应的语言到请求对象
 */
export function languageMiddleware(
  req: Request,
  res: Response,
  next: NextFunction
) {
  try {
    // 获取Accept-Language请求头
    const acceptLanguage = req.headers["accept-language"];
    
    let language: SupportedLanguage = "en";
    
    if (acceptLanguage) {
      // 解析语言代码，例如 "zh-CN,zh;q=0.9,en;q=0.8" => ["zh-CN", "zh", "en"]
      const languages = acceptLanguage
        .split(",")
        .map(lang => lang.split(";")[0].trim().toLowerCase());
      
      // 查找第一个支持的语言
      const supportedLang = languages.find(lang => 
        SUPPORTED_LANGUAGES.includes(lang.split("-")[0] as SupportedLanguage)
      );
      
      if (supportedLang) {
        // 设置语言（使用主要语言代码，例如 "zh-CN" => "zh"）
        language = supportedLang.split("-")[0] as SupportedLanguage;
      }
    }
    
    // 将语言设置到请求对象上
    req.language = language;
    
    // 在请求上下文中存储语言信息
    requestContextStorage.run({ language }, () => {
      next();
    });
  } catch (err) {
    console.error("Language middleware error:", err);
    // 发生错误时使用默认语言，继续处理请求
    req.language = "en";
    
    // 在请求上下文中存储默认语言
    requestContextStorage.run({ language: "en" }, () => {
      next();
    });
  }
}