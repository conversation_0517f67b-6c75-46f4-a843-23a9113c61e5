import { Response, NextFunction } from 'express';
import { MyRequest } from '../types/customRequest';
import { errorResponse } from '../utils/responseUtil';
import { tFromRequest } from '../i18n';

/**
 * 管理员权限验证中间件
 * 验证用户是否具有管理员权限
 */

// 管理员钱包地址列表（可以从环境变量或数据库读取）
const ADMIN_WALLETS = (process.env.ADMIN_WALLETS || '').split(',').filter(Boolean);

// 开发环境下的测试管理员地址
const DEV_ADMIN_WALLETS = [
  'test_admin_wallet',
  'dev_admin_wallet'
];

/**
 * 检查是否为管理员钱包地址
 */
function isAdminWallet(walletAddress: string): boolean {
  // 生产环境检查
  if (process.env.NODE_ENV === 'production') {
    return ADMIN_WALLETS.includes(walletAddress);
  }
  
  // 开发环境检查
  return ADMIN_WALLETS.includes(walletAddress) || DEV_ADMIN_WALLETS.includes(walletAddress);
}

/**
 * 管理员权限验证中间件
 */
export function adminAuthMiddleware(req: MyRequest, res: Response, next: NextFunction): void {
  try {
    // 检查用户是否已登录
    if (!req.user || !req.user.walletAddress) {
      res.status(401).json(errorResponse(
        tFromRequest(req, 'errors.unauthorized') || '请先登录'
      ));
      return;
    }

    const walletAddress = req.user.walletAddress;

    // 检查是否为管理员
    if (!isAdminWallet(walletAddress)) {
      res.status(403).json(errorResponse(
        tFromRequest(req, 'errors.forbidden') || '权限不足，需要管理员权限'
      ));
      return;
    }

    // 记录管理员操作日志
    console.log(`管理员操作: ${walletAddress} - ${req.method} ${req.path}`, {
      timestamp: new Date().toISOString(),
      walletAddress,
      method: req.method,
      path: req.path,
      body: req.method === 'POST' || req.method === 'PUT' ? req.body : undefined,
      userAgent: req.get('User-Agent'),
      ip: req.ip
    });

    next();
  } catch (error) {
    console.error('管理员权限验证失败:', error);
    res.status(500).json(errorResponse(
      tFromRequest(req, 'errors.serverError') || '服务器内部错误'
    ));
  }
}

/**
 * 开发环境管理员权限验证中间件（更宽松的验证）
 */
export function devAdminAuthMiddleware(req: MyRequest, res: Response, next: NextFunction): void {
  // 仅在开发环境使用
  if (process.env.NODE_ENV !== 'development') {
    adminAuthMiddleware(req, res, next);
    return;
  }

  try {
    // 开发环境下，如果没有用户信息，创建一个测试用户
    if (!req.user) {
      req.user = {
        walletId: 1,
        walletAddress: 'dev_admin_wallet',
        userId: 1
      };
    }

    // 记录开发环境操作日志
    console.log(`开发环境管理员操作: ${req.user.walletAddress} - ${req.method} ${req.path}`);

    next();
  } catch (error) {
    console.error('开发环境管理员权限验证失败:', error);
    res.status(500).json(errorResponse('服务器内部错误'));
  }
}

/**
 * 安全警告中间件
 * 在管理员操作前显示安全警告
 */
export function securityWarningMiddleware(req: MyRequest, res: Response, next: NextFunction): void {
  // 对于危险操作添加额外的安全检查
  const dangerousOperations = ['DELETE', 'PUT'];
  const dangerousPaths = ['/activate', '/delete', '/rollback'];
  
  const isDangerous = dangerousOperations.includes(req.method) || 
                     dangerousPaths.some(path => req.path.includes(path));

  if (isDangerous) {
    // 检查是否包含确认标头
    const confirmHeader = req.get('X-Confirm-Operation');
    if (!confirmHeader || confirmHeader !== 'true') {
      res.status(400).json(errorResponse(
        '危险操作需要确认。请在请求头中添加 X-Confirm-Operation: true'
      ));
      return;
    }

    // 记录危险操作
    console.warn(`危险操作执行: ${req.user?.walletAddress} - ${req.method} ${req.path}`, {
      timestamp: new Date().toISOString(),
      walletAddress: req.user?.walletAddress,
      method: req.method,
      path: req.path,
      body: req.body,
      confirmed: true
    });
  }

  next();
}

/**
 * 操作频率限制中间件
 * 限制管理员操作的频率
 */
const operationCounts = new Map<string, { count: number; resetTime: number }>();

export function rateLimitMiddleware(maxOperations: number = 10, windowMs: number = 60000) {
  return (req: MyRequest, res: Response, next: NextFunction): void => {
    const walletAddress = req.user?.walletAddress;
    if (!walletAddress) {
      next();
      return;
    }

    const now = Date.now();
    const userKey = `${walletAddress}:${req.path}`;
    const userOps = operationCounts.get(userKey);

    if (!userOps || now > userOps.resetTime) {
      // 重置计数器
      operationCounts.set(userKey, {
        count: 1,
        resetTime: now + windowMs
      });
      next();
      return;
    }

    if (userOps.count >= maxOperations) {
      res.status(429).json(errorResponse(
        `操作过于频繁，请在 ${Math.ceil((userOps.resetTime - now) / 1000)} 秒后重试`
      ));
      return;
    }

    userOps.count++;
    next();
  };
}

/**
 * 添加管理员钱包地址
 */
export function addAdminWallet(walletAddress: string): void {
  if (!ADMIN_WALLETS.includes(walletAddress)) {
    ADMIN_WALLETS.push(walletAddress);
    console.log(`添加管理员钱包: ${walletAddress}`);
  }
}

/**
 * 移除管理员钱包地址
 */
export function removeAdminWallet(walletAddress: string): void {
  const index = ADMIN_WALLETS.indexOf(walletAddress);
  if (index > -1) {
    ADMIN_WALLETS.splice(index, 1);
    console.log(`移除管理员钱包: ${walletAddress}`);
  }
}

/**
 * 获取管理员钱包列表
 */
export function getAdminWallets(): string[] {
  return [...ADMIN_WALLETS];
}

/**
 * 检查钱包是否为管理员
 */
export function checkIsAdmin(walletAddress: string): boolean {
  return isAdminWallet(walletAddress);
}
