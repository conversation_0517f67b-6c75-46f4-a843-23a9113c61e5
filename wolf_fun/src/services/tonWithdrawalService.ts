// src/services/tonWithdrawalService.ts
import { TonClient, WalletContractV4, internal } from "@ton/ton";
import { mnemonicToPrivateKey } from "@ton/crypto";
import { Withdrawal, WalletHistory } from "../models";
import { sequelize } from "../config/db";
import { Op } from "sequelize";
import dotenv from "dotenv";

dotenv.config();

// 日志工具
const logger = {
  info: (message: string, ...args: any[]) => {
    console.log(`[TonWithdrawalService] ${message}`, ...args);
  },
  error: (message: string, ...args: any[]) => {
    console.error(`[TonWithdrawalService] ${message}`, ...args);
  },
  warn: (message: string, ...args: any[]) => {
    console.warn(`[TonWithdrawalService] ${message}`, ...args);
  },
};

/**
 * TON提现服务
 * 处理TON提现的核心业务逻辑
 */
export class TonWithdrawalService {
  private client: TonClient;
  private contract: any;
  private keyPair: any;
  
  /**
   * 构造函数
   * 初始化TON客户端和钱包合约
   */
  constructor() {
    // 初始化一些属性
    this.client = new TonClient({
      endpoint: 'https://toncenter.com/api/v2/jsonRPC',
      apiKey: process.env.TONCENTER_API_KEY
    });
    this.contract = null;
    this.keyPair = null;
  }
  
  /**
   * 初始化TON客户端和钱包
   * 需要在使用其他方法前调用
   */
  async initialize() {
    try {
      // 初始化TON客户端
      this.client = new TonClient({
        endpoint: 'https://testnet.toncenter.com/api/v2/jsonRPC',
        apiKey: process.env.TONCENTER_API_KEY_TEST,
      });
      
      // 检查钱包助记词配置
      if (!process.env.WALLET_A_SEED) {
        throw new Error("钱包助记词未配置");
      }
      
      // 使用环境变量中的助记词生成密钥对
      const mnemonics = process.env.WALLET_A_SEED.split(" ");
      this.keyPair = await mnemonicToPrivateKey(mnemonics);

      console.log('this.keyPair.publicKey',this.keyPair.publicKey.toString());

      // return;
      
      
      // 创建钱包合约
      const workchain = 0; // 通常使用workchain 0
      const wallet = WalletContractV4.create({
        workchain,
        publicKey: this.keyPair.publicKey,
      });

      // console.log(wallet.address.toString());
      // return;
      
      
      this.contract = this.client.open(wallet);
      
      // 获取钱包余额用于调试
      const balance = await this.contract.getBalance();
      logger.info(`钱包余额: ${balance} TON`);
      
      return true;
    } catch (error) {
      logger.error("初始化TON提现服务失败:", error);
      throw error;
    }
  }
  
  /**
   * 获取待处理的TON提现记录
   * @returns 待处理的提现记录数组
   */
  async getPendingWithdrawals() {
    try {
      // 查找所有状态为'approved'但尚未处理的TON提现记录
      const pendingWithdrawals = await Withdrawal.findAll({
        where: {
          currency: 'ton',
          status: 'approved',
          txStatus: 'pending'
        }
      });
      
      logger.info(`找到 ${pendingWithdrawals.length} 条待处理的TON提现记录`);
      return pendingWithdrawals;
    } catch (error) {
      logger.error("获取待处理TON提现记录失败:", error);
      throw error;
    }
  }
  
  /**
   * 处理TON提现
   * 执行TON转账操作并更新提现记录状态
   */
  async processTonWithdrawals() {
    logger.info("开始处理TON提现任务");
    
    // 使用事务确保数据一致性
    const transaction = await sequelize.transaction();
    
    try {
      // 1. 获取待处理的提现记录
      const pendingWithdrawals = await this.getPendingWithdrawals();
      
      if (pendingWithdrawals.length === 0) {
        await transaction.commit();
        logger.info("没有待处理的TON提现记录");
        return { success: true, processed: 0 };
      }
      
      // 2. 初始化TON服务
      await this.initialize();
      
      // 4. 处理每个提现请求
      let successCount = 0;
      let failCount = 0;
      let retryCount = 0;
      const maxRetries = 1; // 最大重试次数
      const transactionDelay = 5000; // 交易间延迟(毫秒)
      
      // 添加辅助函数用于延迟执行
      const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));
      
  
      for (const withdrawal of pendingWithdrawals) {
        let currentRetry = 0;
        let success = false;
        
        while (!success && currentRetry < maxRetries) {
          try {
            // 3. 为每笔交易重新获取序列号
            const seqno = await this.contract.getSeqno();
            logger.info(`处理提现ID: ${withdrawal.id}, 地址: ${withdrawal.withdrawalAddress}, 金额: ${withdrawal.amount}, 序列号: ${seqno}`);
            
            // 计算实际转账金额和附加的gas费用
            const transferAmount = withdrawal.amount.toString();
          
            logger.info(`提现金额: ${transferAmount} TON`);
            
            // 准备转账信息
            const transfer = this.contract.createTransfer({
              seqno,
              secretKey: this.keyPair.secretKey,
              messages: [
                internal({
                  // 添加额外的gas费用以确保交易有足够的gas
                  value: transferAmount,
                  to: withdrawal.withdrawalAddress,
                  body: `MooFun - Withdrawal #${withdrawal.id}`,
                }),
              ],
            });
            
            // 发送交易并获取结果
            const result = await this.contract.send(transfer);
            logger.info(`交易发送成功: ${JSON.stringify(result)}`);
            
            // 更新提现记录
            await withdrawal.update({
              txHash: seqno.toString(),
              txStatus: 'sent',
              txTimestamp: new Date(),
              status: 'completed',
              processedAt: new Date(),
              processedBy: 'system',
            }, { transaction });
            
            logger.info(`提现ID ${withdrawal.id} 处理成功，交易哈希: ${result.hash}`);
            successCount++;
            success = true;
            
            // 在处理下一个交易之前添加延迟，给区块链足够时间处理当前交易
            await delay(transactionDelay);
          } catch (error: unknown) {
            currentRetry++;
            retryCount++;
            
            // 获取详细的错误信息
            const errorMessage = error instanceof Error ? error.message : String(error);
            const errorStack = error instanceof Error ? error.stack : 'No stack trace';
            
            if (currentRetry < maxRetries) {
              // 如果还有重试机会，记录警告并准备重试
              logger.warn(`提现ID ${withdrawal.id} 处理失败(尝试 ${currentRetry}/${maxRetries}): ${errorMessage}`);
              logger.warn(`错误堆栈: ${errorStack}`);
              
              // 指数退避策略：随着重试次数增加，等待时间变长
              const retryDelay = transactionDelay * Math.pow(2, currentRetry - 1);
              logger.info(`将在 ${retryDelay/1000} 秒后重试...`);
              await delay(retryDelay);
            } else {
              // 达到最大重试次数，记录错误并标记为失败
              logger.error(`提现ID ${withdrawal.id} 处理失败(已尝试 ${maxRetries} 次): ${errorMessage}`);
              logger.error(`错误堆栈: ${errorStack}`);
              failCount++;
              
              // 更新提现记录为失败状态
              await withdrawal.update({
                txStatus: 'failed',
                status: 'failed',
                processedAt: new Date(),
                processedBy: 'system',
                remark: `处理失败(尝试 ${maxRetries} 次): ${errorMessage}`,
              }, { transaction });
            }
          }
        } // while 循环结束
      } // for 循环结束
      
      // 提交事务
      await transaction.commit();
      logger.info(`TON提现处理完成: 成功 ${successCount} 条, 失败 ${failCount} 条, 总重试 ${retryCount} 次`);
      
      return {
        success: true,
        processed: successCount,
        failed: failCount,
        retries: retryCount
      };
    } catch (error) {
      // 出错时回滚事务
      await transaction.rollback();
      logger.error("处理TON提现失败:", error);
      throw error;
    }
  }
}

// 导出服务实例
export const tonWithdrawalService = new TonWithdrawalService();