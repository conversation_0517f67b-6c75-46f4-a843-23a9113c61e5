import { DeliveryLine } from '../models/DeliveryLine';
import { DeliveryLineConfig } from '../models/DeliveryLineConfig';
import { UserWallet } from '../models/UserWallet';
import { sequelize } from '../config/db';
import { DeliveryLineCalculator, formatToThreeDecimalsNumber, createBigNumber } from '../utils/bigNumberConfig';

class DeliveryLineService {
  // 初始化用户的出货线
  public async initializeUserDeliveryLine(walletId: number): Promise<DeliveryLine> {
    const existingLine = await DeliveryLine.findOne({ where: { walletId } });

    // 如果用户已有出货线，直接返回
    if (existingLine) {
      return existingLine;
    }

    // 使用配置创建新的出货线
    return await DeliveryLine.initializeWithConfig(walletId);
  }
  
  // 获取用户的出货线
  public async getUserDeliveryLine(walletId: number): Promise<DeliveryLine & { hasBoost?: boolean, boostMultiplier?: number }> {
    // 导入iapController以获取VIP和加速道具效果
    const iapController = require('../controllers/iapController').default;
    
    // 获取用户的VIP效果和加速道具效果
    const vipEffects = await iapController.getVipEffects(walletId);
    const boosterEffects = await iapController.getBoosterEffects(walletId);
    
    // 计算总的出货线速度加成倍率
    // VIP提供30%的出货线速度加成，Speed Boost提供额外的倍率
    const deliverySpeedMultiplier = vipEffects.deliverySpeedMultiplier * boosterEffects.speedMultiplier;
    
    // 计算出货线价格加成倍率（只有VIP提供20%的价格加成）
    const blockPriceMultiplier = vipEffects.blockPriceMultiplier;
    
    // 先尝试查找用户的出货线
    let deliveryLine = await DeliveryLine.findOne({ where: { walletId } });
    
    // 如果不存在，则初始化一个
    if (!deliveryLine) {
      deliveryLine = await this.initializeUserDeliveryLine(walletId);
    }
    
    // 处理可能的出货
    // await this.processDelivery(deliveryLine);
    
    // 获取基础值
    const baseDeliverySpeed = deliveryLine.deliverySpeed;
    const baseBlockPrice = deliveryLine.blockPrice;

    // 计算下次升级的增长信息（考虑当前加成效果）
    const nextUpgradeGrowth = await this.calculateNextUpgradeGrowthWithBoosts(deliveryLine, deliverySpeedMultiplier, blockPriceMultiplier);

    // 创建结果对象并应用加成，排除 pendingBlocks 字段
    const deliveryLineData = deliveryLine.toJSON();
    const { pendingBlocks, ...resultWithoutPendingBlocks } = deliveryLineData;

    const result = resultWithoutPendingBlocks as DeliveryLine & {
      hasBoost: boolean,
      boostMultiplier: number,
      nextUpgradeGrowth: any
    };

    // 应用加成效果到出货线速度（速度越低越好，所以是除以倍率）
    result.deliverySpeed = baseDeliverySpeed / deliverySpeedMultiplier;

    // 应用加成效果到方块价格
    result.blockPrice = baseBlockPrice * blockPriceMultiplier;

    // 添加加成信息
    result.hasBoost = deliverySpeedMultiplier > 1 || blockPriceMultiplier > 1;
    result.boostMultiplier = deliverySpeedMultiplier;

    // 添加下次升级增长信息
    result.nextUpgradeGrowth = nextUpgradeGrowth;

    return result;
  }
  
  // 升级出货线
  public async upgradeDeliveryLine(walletId: number): Promise<any> {
    const transaction = await sequelize.transaction();

    try {
      // 导入iapController以获取VIP和加速道具效果
      const iapController = require('../controllers/iapController').default;

      // 获取用户的VIP效果和加速道具效果
      const vipEffects = await iapController.getVipEffects(walletId);
      const boosterEffects = await iapController.getBoosterEffects(walletId);

      // 计算总的出货线速度加成倍率
      const deliverySpeedMultiplier = vipEffects.deliverySpeedMultiplier * boosterEffects.speedMultiplier;

      // 计算出货线价格加成倍率（只有VIP提供20%的价格加成）
      const blockPriceMultiplier = vipEffects.blockPriceMultiplier;

      // 获取用户钱包和出货线
      const userWallet = await UserWallet.findOne({ where: { id: walletId }, transaction });
      const deliveryLine = await DeliveryLine.findOne({ where: { walletId }, transaction });

      if (!userWallet || !deliveryLine) {
        throw new Error('用户钱包或出货线不存在');
      }

      // 检查是否可以升级
      const canUpgrade = await deliveryLine.canUpgrade();
      if (!canUpgrade) {
        throw new Error('已达到最高等级，无法升级');
      }

      // 获取升级成本
      const upgradeCost = await deliveryLine.getUpgradeCost();
      if (!upgradeCost) {
        throw new Error('无法获取升级成本');
      }

      // 检查GEM是否足够
      const currentGem = Number(userWallet.gem || 0);
      if (currentGem < upgradeCost) {
        throw new Error('GEM不足，无法升级出货线');
      }

      // 扣除升级费用
      const newGemBN = createBigNumber(userWallet.gem || 0).minus(upgradeCost);
      userWallet.gem = newGemBN.toFixed(3);
      await userWallet.save({ transaction });

      // 使用配置升级出货线
      await deliveryLine.upgradeWithConfig();
      await deliveryLine.save({ transaction });

      await transaction.commit();

      // 获取基础值
      const baseDeliverySpeed = deliveryLine.deliverySpeed;
      const baseBlockPrice = deliveryLine.blockPrice;

      // 创建包含 nextUpgradeGrowth 的响应对象，与农场区API保持一致
      const deliveryLineData = deliveryLine.get({ plain: true });
      const { pendingBlocks, ...deliveryLineWithoutPendingBlocks } = deliveryLineData;

      const deliveryLineDetails = {
        ...deliveryLineWithoutPendingBlocks,
        // 应用加成效果到出货线速度（速度越低越好，所以是除以倍率）
        deliverySpeed: baseDeliverySpeed / deliverySpeedMultiplier,
        // 应用加成效果到方块价格
        blockPrice: baseBlockPrice * blockPriceMultiplier,
        // 添加加成信息
        hasBoost: deliverySpeedMultiplier > 1 || blockPriceMultiplier > 1,
        boostMultiplier: deliverySpeedMultiplier,
        // 添加 nextUpgradeGrowth 数据，确保与农场区API响应格式一致
        nextUpgradeGrowth: await this.calculateNextUpgradeGrowthWithBoosts(deliveryLineData, deliverySpeedMultiplier, blockPriceMultiplier)
      };

      return deliveryLineDetails;
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
  
  // 添加牛奶到出货线
  public async addMilkToDeliveryLine(walletId: number, milkAmount: number): Promise<any> {
    const transaction = await sequelize.transaction();
    
    try {
      // 获取用户钱包和出货线
      const userWallet = await UserWallet.findOne({ where: { id: walletId }, transaction });
      const deliveryLine = await DeliveryLine.findOne({ where: { walletId }, transaction });
      
      if (!userWallet || !deliveryLine) {
        throw new Error('用户钱包或出货线不存在');
      }
      
      // 检查牛奶是否足够
      if (userWallet.milk < milkAmount) {
        throw new Error('牛奶不足');
      }
      
      // 处理可能的出货
      const earnedGem = await this.processDelivery(deliveryLine, transaction);
      const currentGemBN = createBigNumber(userWallet.gem || 0);
      const earnedGemBN = createBigNumber(earnedGem);
      userWallet.gem = currentGemBN.plus(earnedGemBN).toFixed(3);
      
      // 扣除牛奶并添加到出货线
      userWallet.milk -= milkAmount;
      await userWallet.save({ transaction });
      
      // 添加牛奶到出货线
      deliveryLine.addMilk(milkAmount);
      await deliveryLine.save({ transaction });
      
      await transaction.commit();

      // 返回不包含 pendingBlocks 字段的数据
      const deliveryLineData = deliveryLine.get({ plain: true });
      const { pendingBlocks, ...deliveryLineWithoutPendingBlocks } = deliveryLineData;

      return deliveryLineWithoutPendingBlocks;
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
  
  // 处理出货
  public async processDelivery(deliveryLine: DeliveryLine, transaction?: any): Promise<number> {
    // 导入iapController以获取VIP效果
    const iapController = require('../controllers/iapController').default;
    
    // 获取用户的VIP效果（只有VIP提供方块价格加成）
    const vipEffects = await iapController.getVipEffects(deliveryLine.walletId);
    
    // 计算出货获得的基础GEM
    const baseEarnedGem = deliveryLine.deliverBlocks();
    
    // 应用VIP方块价格加成
    const earnedGem = baseEarnedGem * vipEffects.blockPriceMultiplier;
    
    // 如果有出货，保存出货线状态
    if (earnedGem > 0) {
      await deliveryLine.save({ transaction });
    }
    
    return earnedGem;
  }
  
  // 计算离线收益
  public async calculateOfflineEarnings(walletId: number, offlineTime: number): Promise<number> {
    const transaction = await sequelize.transaction();

    try {
      // 导入iapController以获取VIP和加速道具效果
      const iapController = require('../controllers/iapController').default;

      // 获取用户的VIP效果和加速道具效果
      const vipEffects = await iapController.getVipEffects(walletId);
      const boosterEffects = await iapController.getBoosterEffects(walletId);

      // 计算总的出货线速度加成倍率
      const deliverySpeedMultiplier = vipEffects.deliverySpeedMultiplier * boosterEffects.speedMultiplier;

      // 计算出货线价格加成倍率（只有VIP提供20%的价格加成）
      const blockPriceMultiplier = vipEffects.blockPriceMultiplier;

      // 获取用户钱包和出货线
      const userWallet = await UserWallet.findOne({ where: { id: walletId }, transaction });
      const deliveryLine = await DeliveryLine.findOne({ where: { walletId }, transaction });

      if (!userWallet || !deliveryLine) {
        throw new Error('用户钱包或出货线不存在');
      }

      // 计算应用加成后的实际出货速度和方块价格
      const actualDeliverySpeed = deliveryLine.deliverySpeed / deliverySpeedMultiplier;
      const actualBlockPrice = deliveryLine.blockPrice * blockPriceMultiplier;

      // 直接计算离线收益，使用应用加成后的数值
      const earnedGem = this.calculateOfflineEarningsWithBoosts(
        deliveryLine,
        offlineTime,
        actualDeliverySpeed,
        actualBlockPrice
      );

      // 更新用户钱包和出货线
      if (earnedGem > 0) {
        const currentGemBN = createBigNumber(userWallet.gem! || 0);

        const addGemBN = createBigNumber(earnedGem);

        userWallet.gem! = currentGemBN.plus(addGemBN).toFixed(3);
        await userWallet.save({ transaction });
        await deliveryLine.save({ transaction });
      }

      await transaction.commit();
      return formatToThreeDecimalsNumber(earnedGem);
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
  
  // 出售牛奶方块换取宝石
  public async sellMilkBlocks(walletId: number, blockCount: number): Promise<{ soldBlocks: number, earnedGems: number }> {
    const transaction = await sequelize.transaction();
    
    try {
      // 获取用户钱包和出货线
      const userWallet = await UserWallet.findOne({ where: { id: walletId }, transaction });
      const deliveryLine = await DeliveryLine.findOne({ where: { walletId }, transaction });
      
      if (!userWallet || !deliveryLine) {
        throw new Error('用户钱包或出货线不存在');
      }
      
      // 先处理可能的出货
      await this.processDelivery(deliveryLine, transaction);
      
      // 检查是否有足够的牛奶方块
      if (deliveryLine.pendingBlocks < blockCount) {
        throw new Error('没有足够的牛奶方块可供出售');
      }
      
      // 计算获得的宝石数量
      const earnedGems = blockCount * deliveryLine.blockPrice;
      
      // 减少待出售的方块数量
      deliveryLine.pendingBlocks -= blockCount;
      await deliveryLine.save({ transaction });
      
      // 增加用户钱包中的宝石数量
      const currentGemBN = createBigNumber(userWallet.gem! || 0);

      const addGemBN = createBigNumber(earnedGems);

      userWallet.gem! = currentGemBN.plus(addGemBN).toFixed(3);
      await userWallet.save({ transaction });
      
      await transaction.commit();
      return { soldBlocks: blockCount, earnedGems };
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  // 计算下次升级的增长信息（使用配置）
  private async calculateNextUpgradeGrowth(deliveryLine: any): Promise<{
    nextDeliverySpeed: number;
    nextBlockUnit: number;
    nextBlockPrice: number;
  }> {
    const nextConfig = await DeliveryLineConfig.getConfigByGrade(deliveryLine.level + 1);

    if (!nextConfig) {
      // 如果没有下一级配置，返回当前值
      return {
        nextDeliverySpeed: deliveryLine.deliverySpeed,
        nextBlockUnit: deliveryLine.blockUnit,
        nextBlockPrice: deliveryLine.blockPrice,
      };
    }

    return {
      nextDeliverySpeed: formatToThreeDecimalsNumber(nextConfig.production_interval),
      nextBlockUnit: formatToThreeDecimalsNumber(nextConfig.capacity),
      nextBlockPrice: formatToThreeDecimalsNumber(nextConfig.profit),
    };
  }

  // 计算下次升级的增长信息（旧版本，保持向后兼容）
  private calculateNextUpgradeGrowthLegacy(deliveryLine: any): {
    nextDeliverySpeed: number;
    nextBlockUnit: number;
    nextBlockPrice: number;
  } {
    // 使用BigNumber进行高精度计算，确保3位小数精度
    const nextDeliverySpeed = formatToThreeDecimalsNumber(
      DeliveryLineCalculator.calculateUpgradedSpeed(deliveryLine.deliverySpeed)
    );

    const nextBlockUnit = formatToThreeDecimalsNumber(
      DeliveryLineCalculator.calculateUpgradedUnit(deliveryLine.blockUnit)
    );

    const nextBlockPrice = formatToThreeDecimalsNumber(
      DeliveryLineCalculator.calculateUpgradedPrice(deliveryLine.blockPrice)
    );

    return {
      nextDeliverySpeed,
      nextBlockUnit,
      nextBlockPrice,
    };
  }

  // 计算下次升级的增长信息（考虑加成效果）
  private async calculateNextUpgradeGrowthWithBoosts(
    deliveryLine: any,
    deliverySpeedMultiplier: number,
    blockPriceMultiplier: number
  ): Promise<{
    nextDeliverySpeed: number;
    nextBlockUnit: number;
    nextBlockPrice: number;
  }> {
    const nextConfig = await DeliveryLineConfig.getConfigByGrade(deliveryLine.level + 1);

    if (!nextConfig) {
      // 如果没有下一级配置，返回当前值（应用加成）
      return {
        nextDeliverySpeed: formatToThreeDecimalsNumber(deliveryLine.deliverySpeed / deliverySpeedMultiplier),
        nextBlockUnit: formatToThreeDecimalsNumber(deliveryLine.blockUnit),
        nextBlockPrice: formatToThreeDecimalsNumber(deliveryLine.blockPrice * blockPriceMultiplier),
      };
    }

    // 使用配置计算升级后的基础值，然后应用加成效果
    const nextDeliverySpeed = formatToThreeDecimalsNumber(
      nextConfig.production_interval / deliverySpeedMultiplier
    );

    const nextBlockUnit = formatToThreeDecimalsNumber(nextConfig.capacity);

    const nextBlockPrice = formatToThreeDecimalsNumber(
      nextConfig.profit * blockPriceMultiplier
    );

    return {
      nextDeliverySpeed,
      nextBlockUnit,
      nextBlockPrice,
    };
  }

  // 计算离线收益（考虑加成效果）
  private calculateOfflineEarningsWithBoosts(
    deliveryLine: DeliveryLine,
    offlineTime: number,
    actualDeliverySpeed: number,
    actualBlockPrice: number
  ): number {
    // 确保lastDeliveryTime是有效的Date对象
    if (!(deliveryLine.lastDeliveryTime instanceof Date) || isNaN(deliveryLine.lastDeliveryTime.getTime())) {
      deliveryLine.lastDeliveryTime = new Date();
      return 0;
    }

    // 计算离线期间可以完成的出货周期数（使用应用加成后的速度）
    const cycles = Math.floor(offlineTime / actualDeliverySpeed);

    if (cycles <= 0 || deliveryLine.pendingBlocks <= 0) {
      return 0;
    }

    // 计算离线期间可以出售的方块数量
    const deliveredBlocks = Math.min(cycles, deliveryLine.pendingBlocks);

    // 计算获得的GEM数量（使用应用加成后的价格）
    const earnedGem = deliveredBlocks * actualBlockPrice;

    // 减少待出售的方块数量
    deliveryLine.pendingBlocks -= deliveredBlocks;

    // 更新上次出货时间（使用应用加成后的速度）
    const now = new Date();
    deliveryLine.lastDeliveryTime = new Date(Math.min(
      now.getTime(),
      deliveryLine.lastDeliveryTime.getTime() + deliveredBlocks * actualDeliverySpeed * 1000
    ));

    return earnedGem;
  }
}

export default new DeliveryLineService();