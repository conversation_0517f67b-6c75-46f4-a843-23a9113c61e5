import { sequelize } from "../config/db";
import {
    User,
    UserWallet,
    Chest,
    JackpotPool,
    ChestCountdown,
    ChestBoost,
    ShareBoostLink,
    WalletHistory,
    Announcement,
    PaymentRequest
} from "../models";
import { Op, QueryTypes, Transaction } from "sequelize";
import { v4 as uuidv4 } from "uuid";
import dayjs from "dayjs";
import { t } from "../i18n";
import { generateChestReward, processOpenChest } from "./chestService";
import BigNumber from 'bignumber.js';

// Jackpot宝箱配置
export const JACKPOT_CONFIG = {
    LEVEL1: {
        NEW_USER_CONTRIBUTION: 0.01, // 新用户贡献
        TARGET_AMOUNT: 10 // 目标金额
    },
    LEVEL2: {
        NEW_USER_CONTRIBUTION: 0.01, // 新用户贡献
        CHEST_OPEN_CONTRIBUTION: 0.0001, // 开宝箱贡献
        TARGET_AMOUNT: 20 // 目标金额
    },
    LEVEL3: {
        NEW_USER_CONTRIBUTION: 0.01, // 新用户贡献
        CHEST_OPEN_CONTRIBUTION: 0.0001, // 开宝箱贡献
        TARGET_AMOUNT: 50 // 目标金额 (可以根据需求调整)
    },
    COUNTDOWN_HOURS: 24, // 倒计时小时数
    BOOST_REWARDS: {
        SHARE: {
            LEVEL3: { MINUTES: 60, GEM: 50 }, // 3级宝箱助力奖励
            LEVEL4: { MINUTES: 120, GEM: 100 } // 4级宝箱助力奖励
        },
        REFERRAL: {
            LEVEL1: { TIER1: 10, TIER2: 5 }, // 1级宝箱推广加速(分钟)
            LEVEL2: { TIER1: 30, TIER2: 15 }, // 2级宝箱推广加速(分钟)
            LEVEL3: { TIER1: 60, TIER2: 30 }, // 3级宝箱推广加速(分钟)
            LEVEL4: { TIER1: 720, TIER2: 360 } // 4级宝箱推广加速(分钟)
        }
    },
    SHARE_LINK_EXPIRY_HOURS: 24, // 分享链接有效期(小时)
    MAX_SHARE_USES: 12, // 最大分享使用次数
    //jackpot宝箱奖励 10 ton
    REWARDS: 10

};

/**
 * 初始化Jackpot奖池
 */
export async function initializeJackpotPools(transaction: any) {

    try {
        // 检查是否已存在奖池
        const level1Pool = await JackpotPool.findOne({
            where: { level: 1 },
            transaction
        });

        if (!level1Pool) {
            // 创建第一级奖池
            await JackpotPool.create({
                level: 1,
                currentAmount: 0,
                newUserAmount: 0,
                chestOpenAmount: 0,
                targetAmount: JACKPOT_CONFIG.LEVEL1.TARGET_AMOUNT
            }, { transaction });
        }

        // 检查是否已存在第二级奖池
        const level2Pool = await JackpotPool.findOne({
            where: { level: 2 },
            transaction
        });

        if (!level2Pool) {
            // 创建第二级奖池
            await JackpotPool.create({
                level: 2,
                currentAmount: 0,
                newUserAmount: 0,
                chestOpenAmount: 0,
                targetAmount: JACKPOT_CONFIG.LEVEL2.TARGET_AMOUNT
            }, { transaction });
        }

        // 检查是否已存在第三级奖池
        const level3Pool = await JackpotPool.findOne({
            where: { level: 3 },
            transaction
        });

        if (!level3Pool) {
            // 创建第三级奖池
            await JackpotPool.create({
                level: 3,
                currentAmount: 0,
                newUserAmount: 0,
                chestOpenAmount: 0,
                targetAmount: JACKPOT_CONFIG.LEVEL3.TARGET_AMOUNT
            }, { transaction });
        }
        return true;
    } catch (error) {
        console.error("初始化Jackpot奖池失败:", error);
        throw error;
    }
}



/**
 * 处理新用户注册对Jackpot奖池的贡献
 */
export async function handleNewUserContribution(userId: number, walletId: number, transaction: any) {

    try {
        // 先确保奖池已初始化
        await initializeJackpotPools(transaction);

        // 获取当前活跃的奖池级别
        const activePool = await getActiveJackpotPool(transaction);
        if (!activePool) {
            throw new Error("未找到活跃的Jackpot奖池");
        }

        // 检查当前注册池金额是否已达到10 TON上限
        if (activePool.newUserAmount >= 10) {
            return true; // 如果已达到上限，直接返回
        }

        // 根据当前活跃奖池级别确定贡献金额
        let contributionAmount = 0;

        if (activePool.level === 1) {
            contributionAmount = JACKPOT_CONFIG.LEVEL1.NEW_USER_CONTRIBUTION;
        } else if (activePool.level === 2) {
            contributionAmount = JACKPOT_CONFIG.LEVEL2.NEW_USER_CONTRIBUTION;
        } else {
            contributionAmount = JACKPOT_CONFIG.LEVEL3.NEW_USER_CONTRIBUTION;
        }

        // 确保注册池金额不超过10 TON
        const potentialNewAmount = new BigNumber(activePool.newUserAmount).plus(contributionAmount);
        if (potentialNewAmount.isGreaterThan(10)) {
            contributionAmount = new BigNumber(10).minus(activePool.newUserAmount).toNumber();
        }

        // 只有当有实际贡献金额时才更新
        if (contributionAmount > 0) {
            // 更新注册池金额
            await JackpotPool.increment('newUserAmount', {
                by: contributionAmount,
                where: { id: activePool.id },
                transaction
            });

            // 同时更新总金额
            await JackpotPool.increment('currentAmount', {
                by: contributionAmount,
                where: { id: activePool.id },
                transaction
            });
        }


        // 创建用户的倒计时记录 (新用户可以立即领取第一个宝箱)
        await ChestCountdown.create({
            userId,
            walletId,
            nextAvailableTime: new Date(), // 当前时间，表示可以立即领取
            autoCollect: false
        }, { transaction });

        return true;
    } catch (error) {
        console.error("处理新用户Jackpot贡献失败:", error);
        throw error;
    }
}

/**
 * 获取当前活跃的Jackpot奖池
 */
export async function getActiveJackpotPool(transaction: any) {
    // 按级别顺序查找未满的奖池
    const pools = await JackpotPool.findAll({
        order: [['level', 'ASC']],
        lock: true,
        transaction
    });

    for (const pool of pools) {
        if (!pool.lastWinnerId) {
            return pool;
        }
    }

    // 如果所有奖池都已满，返回最高级别的奖池
    return pools.length > 0 ? pools[pools.length - 1] : null;
}

/**
 * 检查并推进Jackpot奖池级别
 */
export async function checkAndAdvanceJackpotLevel(currentLevel: number, userId: number, walletId: number, transaction: Transaction) {
    try {


        // 如果当前等级已经是最高等级，直接返回
        if (currentLevel >= 3) {
            return false;
        }

        // 获取当前级别的奖池
        const currentPool = await JackpotPool.findOne({
            where: { level: currentLevel },
            transaction,
            lock: true
        });

        if (!currentPool || currentPool.currentAmount < currentPool.targetAmount) {
            // 奖池未满，不需要推进
            return false;
        }

        // 奖池已满，准备推进到下一级
        const nextLevel = currentLevel + 1;

        // 获取下一级奖池
        const nextPool = await JackpotPool.findOne({
            where: { level: nextLevel },
            transaction
        });

        if (!nextPool && nextLevel <= 3) {
            // 如果下一级奖池不存在，则创建
            await JackpotPool.create({
                level: nextLevel,
                currentAmount: 0,
                newUserAmount: 0,
                chestOpenAmount: 0,
                targetAmount: nextLevel === 2 ?
                    JACKPOT_CONFIG.LEVEL2.TARGET_AMOUNT :
                    JACKPOT_CONFIG.LEVEL3.TARGET_AMOUNT
            }, { transaction });
        }
        return true;
    } catch (error) {
        console.error("检查并推进Jackpot奖池级别失败:", error);
        throw error;
    }
}


/**
 * 获取用户的宝箱倒计时状态
 */
export async function getChestCountdownStatus(userId: number, walletId: number) {
    // 查找用户的倒计时记录
    let countdown = await ChestCountdown.findOne({
        where: { userId, walletId }
    });

    // 如果不存在，创建一个新的倒计时记录
    if (!countdown) {
        countdown = await ChestCountdown.create({
            userId,
            walletId,
            nextAvailableTime: new Date(), // 新用户可以立即领取
            autoCollect: false
        });
    }

    const now = new Date();
    const nextAvailableTime = new Date(countdown.nextAvailableTime);

    // 计算剩余时间（毫秒）
    const remainingTime = Math.max(0, nextAvailableTime.getTime() - now.getTime());

    // 转换为小时、分钟、秒
    const remainingHours = Math.floor(remainingTime / (1000 * 60 * 60));
    const remainingMinutes = Math.floor((remainingTime % (1000 * 60 * 60)) / (1000 * 60));
    const remainingSeconds = Math.floor((remainingTime % (1000 * 60)) / 1000);

    return {
        canCollect: remainingTime <= 0,
        autoCollect: countdown.autoCollect,
        nextAvailableTime: countdown.nextAvailableTime,
        remainingTime: {
            total: remainingTime,
            hours: remainingHours,
            minutes: remainingMinutes,
            seconds: remainingSeconds
        }
    };
}

/**
 * 领取倒计时宝箱
 */
export async function collectJackpotChest(userId: number, walletId: number, transaction: Transaction) {
    // const transaction = await sequelize.transaction();
    try {
        // 获取用户的倒计时状态
        const countdown = await ChestCountdown.findOne({
            where: { userId, walletId },
            transaction,
            lock: true
        });

        if (!countdown) {
            throw new Error(t('errors.countdownNotFound'));
        }

        const now = new Date();

        // 检查是否可以领取
        if (new Date(countdown.nextAvailableTime) > now) {
            throw new Error(t('errors.chestNotAvailableYet'));
        }

        // 创建宝箱
        const chest = await Chest.create({
            userId,
            walletId,
            isOpened: false,
            type: 'countdown' // 标记为倒计时宝箱
        }, { transaction });

        //执行打开宝箱
        const result = await processOpenChest({
            userId,
            walletId: walletId,
            count: 1,
            chestId: chest.id,
            transaction
        });

        // 更新倒计时
        const nextAvailableTime = new Date(now.getTime() + JACKPOT_CONFIG.COUNTDOWN_HOURS * 60 * 60 * 1000);
        await countdown.update({
            nextAvailableTime
        }, { transaction });
        return {
            chest,
            result
        };
    } catch (error) {
        console.error("领取倒计时宝箱失败:", error);
        throw error;
    }
}


/**
 * 处理推广助力
 */
export async function processReferralBoost(userId: number, walletId: number, chestLevel: number, transaction: Transaction) {
    // 获取用户信息
    const user = await User.findByPk(userId, { transaction });

    if (!user || !user.referrerId) {
        return; // 没有推荐人，不需要处理
    }

    // 获取一级推荐人
    const tier1Referrer = await User.findByPk(user.referrerId, { transaction });

    if (!tier1Referrer) {
        return;
    }

    // 获取一级推荐人的钱包
    const tier1Wallet = await UserWallet.findOne({
        where: { userId: tier1Referrer.id },
        transaction
    });

    if (!tier1Wallet) {
        return;
    }

    // 获取一级推荐人的倒计时
    const tier1Countdown = await ChestCountdown.findOne({
        where: { userId: tier1Referrer.id, walletId: tier1Wallet.id },
        transaction
    });

    if (tier1Countdown) {
        // 计算加速时间
        let boostMinutes = 0;

        switch (chestLevel) {
            case 1:
                boostMinutes = JACKPOT_CONFIG.BOOST_REWARDS.REFERRAL.LEVEL1.TIER1;
                break;
            case 2:
                boostMinutes = JACKPOT_CONFIG.BOOST_REWARDS.REFERRAL.LEVEL2.TIER1;
                break;
            case 3:
                boostMinutes = JACKPOT_CONFIG.BOOST_REWARDS.REFERRAL.LEVEL3.TIER1;
                break;
            case 4:
                boostMinutes = JACKPOT_CONFIG.BOOST_REWARDS.REFERRAL.LEVEL4.TIER1;
                break;
        }

        // 只有当倒计时未到0时才加速
        if (new Date(tier1Countdown.nextAvailableTime) > new Date()) {
            const newNextAvailableTime = new Date(
                new Date(tier1Countdown.nextAvailableTime).getTime() - (boostMinutes * 60 * 1000)
            );

            // 确保不会小于当前时间
            const now = new Date();
            if (newNextAvailableTime < now) {
                await tier1Countdown.update({
                    nextAvailableTime: now
                }, { transaction });
            } else {
                await tier1Countdown.update({
                    nextAvailableTime: newNextAvailableTime
                }, { transaction });
            }

            // 记录加速
            await ChestBoost.create({
                sourceUserId: userId,
                sourceWalletId: walletId,
                sourceLevel: 1,
                targetUserId: tier1Referrer.id,
                targetWalletId: tier1Wallet.id,
                boostType: 'referral',
                boostMinutes,
                chestLevel,
                gemAmount: 0, // 推广助力不给宝石
                isProcessed: true,
                expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24小时后过期
            }, { transaction });
        }
    }

    // 处理二级推荐人
    if (tier1Referrer.referrerId) {
        const tier2Referrer = await User.findByPk(tier1Referrer.referrerId, { transaction });

        if (!tier2Referrer) {
            return;
        }

        // 获取二级推荐人的钱包
        const tier2Wallet = await UserWallet.findOne({
            where: { userId: tier2Referrer.id },
            transaction
        });

        if (!tier2Wallet) {
            return;
        }

        // 获取二级推荐人的倒计时
        const tier2Countdown = await ChestCountdown.findOne({
            where: { userId: tier2Referrer.id, walletId: tier2Wallet.id },
            transaction
        });

        if (tier2Countdown) {
            // 计算加速时间
            let boostMinutes = 0;

            switch (chestLevel) {
                case 1:
                    boostMinutes = JACKPOT_CONFIG.BOOST_REWARDS.REFERRAL.LEVEL1.TIER2;
                    break;
                case 2:
                    boostMinutes = JACKPOT_CONFIG.BOOST_REWARDS.REFERRAL.LEVEL2.TIER2;
                    break;
                case 3:
                    boostMinutes = JACKPOT_CONFIG.BOOST_REWARDS.REFERRAL.LEVEL3.TIER2;
                    break;
                case 4:
                    boostMinutes = JACKPOT_CONFIG.BOOST_REWARDS.REFERRAL.LEVEL4.TIER2;
                    break;
            }

            // 只有当倒计时未到0时才加速
            if (new Date(tier2Countdown.nextAvailableTime) > new Date()) {
                const newNextAvailableTime = new Date(
                    new Date(tier2Countdown.nextAvailableTime).getTime() - (boostMinutes * 60 * 1000)
                );

                // 确保不会小于当前时间
                const now = new Date();
                if (newNextAvailableTime < now) {
                    await tier2Countdown.update({
                        nextAvailableTime: now
                    }, { transaction });
                } else {
                    await tier2Countdown.update({
                        nextAvailableTime: newNextAvailableTime
                    }, { transaction });
                }

                // 记录加速
                await ChestBoost.create({
                    sourceUserId: userId,
                    sourceWalletId: walletId,
                    sourceLevel: 2,
                    targetUserId: tier2Referrer.id,
                    targetWalletId: tier2Wallet.id,
                    boostType: 'referral',
                    boostMinutes,
                    chestLevel,
                    gemAmount: 0, // 推广助力不给宝石
                    isProcessed: true,
                    expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24小时后过期
                }, { transaction });
            }
        }
    }
}

/**
 * 获取一次性4个宝箱
 * 每个用户只能获取一次
 */
export async function collectFourChests(userId: number, walletId: number, transaction: Transaction) {
    try {
        // 1. 检查用户是否已经领取过这4个宝箱（通过UserWallet中的hasCollectedFourChests字段判断）
        const userWallet = await UserWallet.findByPk(walletId, { transaction });
        
        if (!userWallet) {
            throw new Error(t('errors.walletNotFound'));
        }
        
        if (userWallet.hasCollectedFourChests) {
            throw new Error(t('errors.alreadyCollectedFourChests'));
        }

        // 2. 创建4个宝箱并立即打开
        const chests = [];
        const results = [];
        
        // 用于合并结果的变量
        const mergedResult = {
            openedCount: 0,
            chestIds: [] as number[],
            rewards: [] as any[],
            summary: {
                ticket: 0,
                fragment_green: 0,
                fragment_blue: 0,
                fragment_purple: 0,
                fragment_gold: 0,
                ton: 0,
                gem: 0
            },
            levelSummary: {
                level1: 0,
                level2: 0,
                level3: 0,
                level4: 0
            },
            shareLinks: [] as any[],
            jackpotWinner: null as null | { level: number; amount: number; userId: number; walletId: number; poolId: number; winTime: Date; }
        };

        for (let i = 0; i < 4; i++) {
            // 创建宝箱
            const chest = await Chest.create({
                userId,
                walletId,
                isOpened: false,
                type: 'four_chests_bonus', // 标记为一次性4个宝箱
                source: 'bonus' // 标记来源为奖励
            }, { transaction });

            chests.push(chest);

            // 执行打开宝箱
            const result = await processOpenChest({
                userId,
                walletId,
                chestId: chest.id,
                transaction
            });

            results.push(result);
            
            // 合并结果
            mergedResult.openedCount += result.openedCount;
            mergedResult.chestIds = [...mergedResult.chestIds, ...result.chestIds];
            mergedResult.rewards = [...mergedResult.rewards, ...result.rewards];
            
            // 合并summary
            Object.keys(result.summary).forEach(key => {
                if (mergedResult.summary.hasOwnProperty(key)) {
                    mergedResult.summary[key as keyof typeof mergedResult.summary] += result.summary[key as keyof typeof result.summary];
                }
            });
            
            // 合并levelSummary
            Object.keys(result.levelSummary).forEach(key => {
                if (mergedResult.levelSummary.hasOwnProperty(key)) {
                    mergedResult.levelSummary[key as keyof typeof mergedResult.levelSummary] += result.levelSummary[key as keyof typeof result.levelSummary];
                }
            });
            
            // 合并shareLinks
            if (result.shareLinks && result.shareLinks.length > 0) {
                mergedResult.shareLinks = [...mergedResult.shareLinks, ...result.shareLinks];
            }
            
            // 如果有jackpotWinner，保存最后一个
            if (result.jackpotWinner) {
                mergedResult.jackpotWinner = result.jackpotWinner;
            }
        }

        // 更新用户钱包的hasCollectedFourChests字段
        await UserWallet.update(
            { hasCollectedFourChests: true },
            { where: { id: walletId }, transaction }
        );
        
        return {
            chests,
            mergedResult // 返回合并后的结果
        };
    } catch (error) {
        console.error("获取一次性4个宝箱失败:", error);
        throw error;
    }
}

/**
 * 获取用户四个宝箱领取状态
 */
export async function getFourChestsCollectStatus(userId: number, walletId: number) {
    try {
        const userWallet = await UserWallet.findByPk(walletId);
        
        if (!userWallet) {
            throw new Error(t('errors.walletNotFound'));
        }
        
        return {
            hasCollected: userWallet.hasCollectedFourChests || false
        };
    } catch (error) {
        console.error("获取四个宝箱领取状态失败:", error);
        throw error;
    }
}

/**
 * 创建分享助力链接
 */
export async function createShareBoostLink(userId: number, walletId: number, chestId: number, chestLevel: number, transaction: any) {
    // 生成唯一助力码
    const code = uuidv4();

    // 设置过期时间
    const expiresAt = new Date(Date.now() + JACKPOT_CONFIG.SHARE_LINK_EXPIRY_HOURS * 60 * 60 * 1000);

    // 创建分享链接
    await ShareBoostLink.create({
        userId,
        walletId,
        chestId,
        chestLevel,
        code,
        maxUses: JACKPOT_CONFIG.MAX_SHARE_USES,
        currentUses: 0,
        expiresAt
    }, { transaction });

    return code;
}

/**
 * 使用分享助力链接
 */
export async function useShareBoostLink(code: string, targetUserId: number, targetWalletId: number) {
    const transaction = await sequelize.transaction();

    try {
        // 查找分享链接
        const shareLink = await ShareBoostLink.findOne({
            where: { code },
            transaction,
            lock: true
        });

        if (!shareLink) {
            throw new Error(t('errors.shareLinkNotFound'));
        }

        // 检查链接是否过期
        if (shareLink.expiresAt < new Date()) {
            throw new Error(t('errors.shareLinkExpired'));
        }

        // 检查使用次数是否达到上限
        if (shareLink.currentUses >= shareLink.maxUses) {
            throw new Error(t('errors.shareLinkMaxUsesReached'));
        }

        // 检查用户是否为自己
        if (shareLink.userId === targetUserId) {
            throw new Error(t('errors.cannotBoostYourself'));
        }

        // 检查是否已经使用过该链接
        const existingBoost = await ChestBoost.findOne({
            where: {
                sourceUserId: shareLink.userId,
                sourceWalletId: shareLink.walletId,
                targetUserId,
                targetWalletId,
                boostType: 'share',
                isProcessed: true
            },
            transaction
        });

        if (existingBoost) {
            throw new Error(t('errors.alreadyUsedShareLink'));
        }

        // 确定奖励
        let boostMinutes = 0;
        let gemAmount = 0;

        if (shareLink.chestLevel === 3) {
            boostMinutes = JACKPOT_CONFIG.BOOST_REWARDS.SHARE.LEVEL3.MINUTES;
            gemAmount = JACKPOT_CONFIG.BOOST_REWARDS.SHARE.LEVEL3.GEM;
        } else if (shareLink.chestLevel === 4) {
            boostMinutes = JACKPOT_CONFIG.BOOST_REWARDS.SHARE.LEVEL4.MINUTES;
            gemAmount = JACKPOT_CONFIG.BOOST_REWARDS.SHARE.LEVEL4.GEM;
        }

        // 更新分享链接使用次数
        await shareLink.increment('currentUses', { transaction });

        // 处理目标用户的倒计时加速
        const targetCountdown = await ChestCountdown.findOne({
            where: { userId: targetUserId, walletId: targetWalletId },
            transaction
        });

        if (targetCountdown && new Date(targetCountdown.nextAvailableTime) > new Date()) {
            const newNextAvailableTime = new Date(
                new Date(targetCountdown.nextAvailableTime).getTime() - (boostMinutes * 60 * 1000)
            );

            // 确保不会小于当前时间
            const now = new Date();
            if (newNextAvailableTime < now) {
                await targetCountdown.update({
                    nextAvailableTime: now
                }, { transaction });
            } else {
                await targetCountdown.update({
                    nextAvailableTime: newNextAvailableTime
                }, { transaction });
            }
        }

        // 给目标用户增加宝石
        await UserWallet.increment('gem', {
            by: gemAmount,
            where: { id: targetWalletId },
            transaction
        });

        // 记录目标用户的钱包历史
        await WalletHistory.create({
            userId: targetUserId,
            walletId: targetWalletId,
            amount: gemAmount,
            currency: 'gem',
            reference: 'Share Boost Reward',
            action: 'in',
            category: 'gem',
            credit_type: 'gem',
            fe_display_remark: `分享助力奖励 - GEM`,
            developer_remark: `用户使用分享助力链接获得奖励，链接ID: ${shareLink.id}`,
        }, { transaction });

        // 处理源用户的倒计时加速
        const sourceCountdown = await ChestCountdown.findOne({
            where: { userId: shareLink.userId, walletId: shareLink.walletId },
            transaction
        });

        if (sourceCountdown && new Date(sourceCountdown.nextAvailableTime) > new Date()) {
            const newNextAvailableTime = new Date(
                new Date(sourceCountdown.nextAvailableTime).getTime() - (boostMinutes * 60 * 1000)
            );

            // 确保不会小于当前时间
            const now = new Date();
            if (newNextAvailableTime < now) {
                await sourceCountdown.update({
                    nextAvailableTime: now
                }, { transaction });
            } else {
                await sourceCountdown.update({
                    nextAvailableTime: newNextAvailableTime
                }, { transaction });
            }
        }

        // 给源用户增加宝石
        await UserWallet.increment('gem', {
            by: gemAmount,
            where: { id: shareLink.walletId },
            transaction
        });

        // 记录源用户的钱包历史
        await WalletHistory.create({
            userId: shareLink.userId,
            walletId: shareLink.walletId,
            amount: gemAmount,
            currency: 'gem',
            reference: 'Share Boost Reward',
            action: 'in',
            category: 'gem',
            credit_type: 'gem',
            fe_display_remark: `分享助力奖励 - GEM`,
            developer_remark: `用户的分享助力链接被使用获得奖励，链接ID: ${shareLink.id}`,
        }, { transaction });

        // 记录助力关系
        await ChestBoost.create({
            sourceUserId: shareLink.userId,
            sourceWalletId: shareLink.walletId,
            targetUserId,
            targetWalletId,
            sourceLevel: 0,
            boostType: 'share',
            boostMinutes,
            chestLevel: shareLink.chestLevel,
            gemAmount,
            isProcessed: true,
            expiresAt: shareLink.expiresAt
        }, { transaction });

        await transaction.commit();

        return {
            boostMinutes,
            gemAmount
        };
    } catch (error) {
        await transaction.rollback();
        console.error("使用分享助力链接失败:", error);
        throw error;
    }
}


/**
 * 开启/关闭自动领取功能
 */
export async function toggleAutoCollect(userId: number, walletId: number, autoCollect: boolean) {
    const transaction = await sequelize.transaction();

    try {
        // 查找用户的倒计时记录
        let countdown = await ChestCountdown.findOne({
            where: { userId, walletId },
            transaction,
            lock: true
        });

        if (!countdown) {
            // 如果不存在，创建一个新的倒计时记录
            countdown = await ChestCountdown.create({
                userId,
                walletId,
                nextAvailableTime: new Date(Date.now() + JACKPOT_CONFIG.COUNTDOWN_HOURS * 60 * 60 * 1000),
                autoCollect
            }, { transaction });
        } else {
            // 更新自动领取状态
            await countdown.update({
                autoCollect
            }, { transaction });
        }

        await transaction.commit();

        return {
            success: true,
            autoCollect
        };
    } catch (error) {
        await transaction.rollback();
        console.error("切换自动领取状态失败:", error);
        throw error;
    }
}


/**
 * 获取Jackpot奖池状态
 */
export async function getJackpotPoolStatus() {
    // 获取所有奖池
    const pools = await JackpotPool.findAll({
        order: [['level', 'ASC']]
    });

    // 获取当前活跃的奖池
    let activePool = null;
    for (const pool of pools) {
        if (!pool.lastWinnerId) {
            activePool = pool;
            break;
        }
    }

    // 如果没有找到活跃奖池，使用最高级别的奖池
    if (!activePool && pools.length > 0) {
        activePool = pools[pools.length - 1];
    }

    // 获取最近的获奖者信息
    const winners = [];
    for (const pool of pools) {
        if (pool.lastWinnerId && pool.lastWinnerWalletId && pool.lastWinTime) {
            const user = await User.findByPk(pool.lastWinnerId);
            const wallet = await UserWallet.findByPk(pool.lastWinnerWalletId);

            if (user && wallet) {
                winners.push({
                    level: pool.level,
                    userId: pool.lastWinnerId,
                    walletId: pool.lastWinnerWalletId,
                    username: user.username,
                    photoUrl: user.photoUrl,
                    walletAddress: wallet.walletAddress,
                    //奖励数量
                    rewardAmount: JACKPOT_CONFIG.REWARDS,
                    winTime: pool.lastWinTime
                });
            }
        }
    }

    return {
        pools: pools.map(pool => ({
            level: pool.level,
            currentAmount: pool.currentAmount,
            targetAmount: pool.targetAmount,
            newUserAmount: pool.newUserAmount,
            chestOpenAmount: pool.chestOpenAmount,
            newUserProgress: Math.floor((pool.newUserAmount / pool.targetAmount) * 100),
            chestOpenProgress: Math.floor((pool.chestOpenAmount / pool.targetAmount) * 100),
            totalProgress: Math.floor((pool.currentAmount / pool.targetAmount) * 100),
            isActive: activePool ? pool.id === activePool.id : false
        })),
        activePool: activePool ? {
            level: activePool.level,
            currentAmount: activePool.currentAmount,
            targetAmount: activePool.targetAmount,
            newUserAmount: activePool.newUserAmount,
            chestOpenAmount: activePool.chestOpenAmount,
            newUserProgress: Math.floor((activePool.newUserAmount / activePool.targetAmount) * 100),
            chestOpenProgress: Math.floor((activePool.chestOpenAmount / activePool.targetAmount) * 100),
            totalProgress: Math.floor((activePool.currentAmount / activePool.targetAmount) * 100)
        } : null,
        recentWinners: winners
    };
}

/**
 * 自动领取倒计时宝箱的定时任务
 */
export async function processAutoCollectChests() {

    try {
        // 查找所有启用了自动领取且倒计时已结束的用户，且必须是Star用户
        const now = new Date();
        const countdowns = await ChestCountdown.findAll({
            where: {
                autoCollect: true,
                nextAvailableTime: {
                    [Op.lte]: now
                }
            },
            include: [{
                model: UserWallet,
                as: 'userWallet',
                where: {
                    isStar: true
                }
            }]
        });

        // console.log(`找到 ${countdowns.length} 个需要自动领取的宝箱`);

        for (const countdown of countdowns) {
            const transaction = await sequelize.transaction();
            try {
                // 为每个用户领取宝箱
                await collectJackpotChest(countdown.userId, countdown.walletId, transaction);
                await transaction.commit();
                console.log(`已为用户 ${countdown.userId} 自动领取宝箱`);
            } catch (error) {
                await transaction.rollback();
                console.error(`为用户 ${countdown.userId} 自动领取宝箱失败:`, error);
                // 继续处理下一个用户
            }
        }
        return true;
    } catch (error) {
        console.error("处理自动领取宝箱失败:", error);
        throw error;
    }
}


/**
 * 获取用户的分享助力链接
 */
export async function getUserShareBoostLinks(userId: number, walletId: number, status: string = 'unused') {
    // 获取用户的分享链接，根据状态过滤
    const now = new Date();
    const whereClause: any = {
        userId,
        walletId
    };

    // 根据状态参数设置过滤条件
    if (status === 'unused') {
        // 未使用的链接：未过期且当前使用次数小于最大使用次数
        whereClause.expiresAt = { [Op.gt]: now };
        whereClause.currentUses = { [Op.lt]: sequelize.col('maxUses') };
    } else if (status === 'used') {
        // 已使用的链接：已过期或当前使用次数等于最大使用次数
        whereClause[Op.or] = [
            { expiresAt: { [Op.lte]: now } },
            { currentUses: { [Op.gte]: sequelize.col('maxUses') } }
        ];
    }
    // 'all' 状态不需要额外的过滤条件

    const links = await ShareBoostLink.findAll({
        where: whereClause,
        order: [['createdAt', 'DESC']]
    });

    return links.map(link => {
        // 判断链接无法使用的原因
        let unusableReason = null;
        if (new Date(link.expiresAt) <= now) {
            unusableReason = t('errors.shareLinkExpired');
        } else if (link.currentUses >= link.maxUses) {
            unusableReason = t('errors.shareLinkMaxUsesReached');
        }

        return {
            id: link.id,
            code: link.code,
            chestLevel: link.chestLevel,
            currentUses: link.currentUses,
            maxUses: link.maxUses,
            expiresAt: link.expiresAt,
            remainingTime: Math.max(0, new Date(link.expiresAt).getTime() - now.getTime()),
            unusableReason
        };
    });
}

/**
 * 使用Telegram分享助力链接 - 不需要钱包认证，只需要Telegram认证
 * @param code 分享链接代码
 * @param userId 用户ID（如果是已注册用户）
 * @param telegramId Telegram用户ID
 * @param isPremium 是否为Telegram Premium用户
 * @returns 助力结果
 */
export async function useTelegramShareBoostLink(code: string, userId: number, telegramId: string, isPremium: boolean) {
    const transaction = await sequelize.transaction();

    try {
        // 查找分享链接
        const shareLink = await ShareBoostLink.findOne({
            where: { code },
            transaction,
            lock: true
        });

        if (!shareLink) {
            throw new Error(t('errors.shareLinkNotFound'));
        }

        // 检查链接是否过期
        if (shareLink.expiresAt < new Date()) {
            throw new Error(t('errors.shareLinkExpired'));
        }

        // 检查使用次数是否达到上限
        if (shareLink.currentUses >= shareLink.maxUses) {
            throw new Error(t('errors.shareLinkMaxUsesReached'));
        }

        // 查找源用户
        const sourceUser = await User.findOne({
            where: { id: shareLink.userId },
            transaction
        });

        if (!sourceUser) {
            throw new Error(t('errors.sourceUserNotFound'));
        }

        // 如果用户ID为0，表示这是一个未注册的Telegram用户
        let targetUser = null;
        let targetWalletId = 0;

        if (userId > 0) {
            // 已注册用户
            targetUser = await User.findOne({
                where: { id: userId },
                transaction
            });

            if (targetUser) {
                // 检查用户是否为自己
                if (shareLink.userId === targetUser.id) {
                    throw new Error(t('errors.cannotBoostYourself'));
                }

                // 获取用户钱包
                const targetWallet = await UserWallet.findOne({
                    where: { userId: targetUser.id },
                    transaction
                });

                if (targetWallet) {
                    targetWalletId = targetWallet.id;

                    // 检查是否已经使用过该链接
                    const existingBoost = await ChestBoost.findOne({
                        where: {
                            sourceUserId: shareLink.userId,
                            sourceWalletId: shareLink.walletId,
                            targetUserId: targetUser.id,
                            targetWalletId: targetWallet.id,
                            boostType: 'telegram_share',
                            isProcessed: true,
                            shareCodeId: shareLink.id // 添加shareCodeId作为判断条件
                        },
                        transaction
                    });

                    if (existingBoost) {
                        throw new Error(t('errors.alreadyUsedShareLink'));
                    }
                }
            }
        } else {
            // 未注册用户 - 检查是否已经使用过该链接（基于telegramId）
            const existingBoost = await ChestBoost.findOne({
                where: {
                    sourceUserId: shareLink.userId,
                    sourceWalletId: shareLink.walletId,
                    targetTelegramId: telegramId,
                    boostType: 'telegram_share',
                    isProcessed: true,
                    shareCodeId: shareLink.id // 使用shareCodeId作为判断条件，替换之前的验证方式
                },
                transaction
            });



            if (existingBoost) {
                throw new Error(t('errors.alreadyUsedShareLink'));
            }
        }

        // 确定奖励
        let boostMinutes = 0;
        let gemAmount = 0;

        if (shareLink.chestLevel === 3) {
            boostMinutes = JACKPOT_CONFIG.BOOST_REWARDS.SHARE.LEVEL3.MINUTES;
            gemAmount = JACKPOT_CONFIG.BOOST_REWARDS.SHARE.LEVEL3.GEM;
        } else if (shareLink.chestLevel === 4) {
            boostMinutes = JACKPOT_CONFIG.BOOST_REWARDS.SHARE.LEVEL4.MINUTES;
            gemAmount = JACKPOT_CONFIG.BOOST_REWARDS.SHARE.LEVEL4.GEM;
        }

        // 更新分享链接使用次数
        await shareLink.increment('currentUses', { transaction });

        // 处理源用户的倒计时加速
        const sourceCountdown = await ChestCountdown.findOne({
            where: { userId: shareLink.userId, walletId: shareLink.walletId },
            transaction
        });

        if (sourceCountdown && new Date(sourceCountdown.nextAvailableTime) > new Date()) {
            const newNextAvailableTime = new Date(
                new Date(sourceCountdown.nextAvailableTime).getTime() - (boostMinutes * 60 * 1000)
            );

            // 确保不会小于当前时间
            const now = new Date();
            if (newNextAvailableTime < now) {
                await sourceCountdown.update({
                    nextAvailableTime: now
                }, { transaction });
            } else {
                await sourceCountdown.update({
                    nextAvailableTime: newNextAvailableTime
                }, { transaction });
            }
        }

        // 给源用户增加宝石
        await UserWallet.increment('gem', {
            by: gemAmount,
            where: { id: shareLink.walletId },
            transaction
        });

        // 记录源用户的钱包历史
        await WalletHistory.create({
            userId: shareLink.userId,
            walletId: shareLink.walletId,
            amount: gemAmount,
            currency: 'gem',
            reference: 'Telegram Share Boost Reward',
            action: 'in',
            category: 'gem',
            credit_type: 'gem',
            fe_display_remark: `Telegram分享助力奖励 - GEM`,
            developer_remark: `用户的分享助力链接被Telegram用户使用获得奖励，链接ID: ${shareLink.id}`,
        }, { transaction });

        // 记录助力关系
        await ChestBoost.create({
            sourceUserId: shareLink.userId,
            sourceWalletId: shareLink.walletId,
            targetUserId: userId > 0 ? userId : null,
            targetWalletId: targetWalletId > 0 ? targetWalletId : null,
            targetTelegramId: telegramId,
            sourceLevel: 0,
            boostType: 'telegram_share',
            boostMinutes,
            chestLevel: shareLink.chestLevel,
            gemAmount,
            isProcessed: true,
            expiresAt: shareLink.expiresAt,
            shareCodeId: shareLink.id // 记录使用的分享码ID
        }, { transaction });

        await transaction.commit();

        return {
            boostMinutes,
            gemAmount,
        };
    } catch (error) {
        await transaction.rollback();
        console.error("使用Telegram分享助力链接失败:", error);
        throw error;
    }
}

export async function savePaymentRequest(paymentId: string, walletId: number, userId: number, amount: number, productType: string) {
    try {
        // 验证参数
        if (!paymentId || !walletId || !userId || !productType) {
            throw new Error('Missing required parameters');
        }

        // 验证支付ID格式
        if (!paymentId.startsWith('star_')) {
            throw new Error('Invalid payment ID format');
        }

        // 验证金额
        if (amount <= 0 || isNaN(amount)) {
            throw new Error('Invalid payment amount');
        }

  
        // 使用事务确保数据一致性
        const result = await sequelize.transaction(async (t) => {
            // 清理过期的支付请求
            await PaymentRequest.update(
                { status: 'expired' },
                {
                    where: {
                        paymentId,
                        status: 'pending'
                    },
                    transaction: t
                }
            );

            // 创建新的支付请求
            const paymentRequest = await PaymentRequest.create({
                paymentId,
                userId,
                walletId,
                amount,
                productType,
                status: 'pending',
                createdAt: new Date(),
                expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24小时后过期
            }, { transaction: t });

            return paymentRequest;
        });

        return {
            id: result.id,
            paymentId: result.paymentId,
            status: result.status,
            amount: result.amount,
            productType: result.productType,
            expiresAt: result.expiresAt,
            createdAt: result.createdAt
        };
    } catch (error) {
        console.error('保存支付请求失败:', error);
        if (error instanceof Error) {
            if (['Payment request already exists', 'User has pending payment request', 'Invalid payment ID format', 'Invalid payment amount', 'Missing required parameters'].includes(error.message)) {
                throw error;
            }
        }
        throw new Error('Failed to save payment request');
    }
}

/**
 * 获取用户作为助力目标的历史记录（收到的助力）
 */
export async function getUserIncomingBoostHistory(userId: number, walletId: number, page: number = 1, pageSize: number = 10) {
    const offset = (page - 1) * pageSize;

    // 获取用户作为目标的助力记录
    const { count, rows: targetBoosts } = await ChestBoost.findAndCountAll({
        where: {
            targetUserId: userId,
            targetWalletId: walletId
        },
        order: [['createdAt', 'DESC']],
        limit: pageSize,
        offset: offset
    });

    // 处理目标助力记录
    const formattedTargetBoosts = await Promise.all(targetBoosts.map(async (boost) => {
        const sourceUser = await User.findByPk(boost.sourceUserId);
        const sourceWallet = await UserWallet.findByPk(boost.sourceWalletId);

        return {
            id: boost.id,
            type: boost.boostType,
            direction: 'incoming',
            chestLevel: boost.chestLevel,
            boostMinutes: boost.boostMinutes,
            sourceLevel: boost.sourceLevel,
            gemAmount: boost.gemAmount,
            createdAt: boost.createdAt,
            sourceUser: {
                userId: boost.sourceUserId,
                username: sourceUser ? sourceUser.username : null,
                photoUrl: sourceUser ? sourceUser.photoUrl : null,
                walletAddress: sourceWallet ? sourceWallet.walletAddress : null
            }
        };
    }));

    return {
        items: formattedTargetBoosts,
        total: count,
        page,
        pageSize,
        totalPages: Math.ceil(count / pageSize)
    };
}


/**
 * 获取分享助力记录
 * 查询特定类型的助力记录（只查询 boostType 为 'share' 的记录）
 */
export async function getShareBoostHistory(userId: number, walletId: number, direction: 'incoming', page: number = 1, pageSize: number = 10) {
    const offset = (page - 1) * pageSize;

    let whereCondition = {};


    // 用户作为目标的分享助力记录
    whereCondition = {
        targetUserId: userId,
        targetWalletId: walletId,
        boostType: 'share'
    };


    // 查询记录
    const { count, rows: boosts } = await ChestBoost.findAndCountAll({
        where: whereCondition,
        order: [['createdAt', 'DESC']],
        limit: pageSize,
        offset: offset
    });

    // 处理记录
    const formattedBoosts = await Promise.all(boosts.map(async (boost) => {

        // 处理收到的助力
        const sourceUser = await User.findByPk(boost.sourceUserId);
        const sourceWallet = await UserWallet.findByPk(boost.sourceWalletId);

        return {
            id: boost.id,
            type: boost.boostType,
            direction: 'incoming',
            chestLevel: boost.chestLevel,
            sourceLevel: boost.sourceLevel,
            boostMinutes: boost.boostMinutes,
            gemAmount: boost.gemAmount,
            createdAt: boost.createdAt,
            sourceUser: {
                userId: boost.sourceUserId,
                username: sourceUser ? sourceUser.username : null,
                photoUrl: sourceUser ? sourceUser.photoUrl : null,
                walletAddress: sourceWallet ? sourceWallet.walletAddress : null
            }
        };

    }));

    return {
        items: formattedBoosts,
        total: count,
        page,
        pageSize,
        totalPages: Math.ceil(count / pageSize)
    };
}

/**
 * 获取用户的加速信息
 * @param userId 用户ID
 * @param walletId 钱包ID
 */
export async function getUserBoostInfo(userId: number, walletId: number, page: number = 1, pageSize: number = 10) {
    try {
        // 获取今日和7天前的日期
        const today = dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss');
        const sevenDaysAgo = dayjs().subtract(7, 'days').startOf('day').format('YYYY-MM-DD HH:mm:ss');
        // 计算分页参数
        const offset = (page - 1) * pageSize;

        // 获取用户的加速信息（只统计 referral 类型）
        const boostInfo = await sequelize.query(
            `SELECT 
          (
            SELECT COALESCE(SUM(cb.boostMinutes), 0)
            FROM chest_boosts cb
            WHERE cb.targetUserId = :userId AND cb.createdAt >= :today AND cb.boostType = 'referral'
          ) as todayBoostMinutes,
          (
            SELECT COALESCE(SUM(cb.boostMinutes), 0)
            FROM chest_boosts cb
            WHERE cb.targetUserId = :userId AND cb.createdAt >= :sevenDaysAgo AND cb.boostType = 'referral'
          ) as weekBoostMinutes,
          (
            SELECT COUNT(*)
            FROM chest_boosts cb
            WHERE cb.targetUserId = :userId AND cb.createdAt >= :today AND cb.boostType = 'referral'
          ) as todayBoostCount,
          (
            SELECT COUNT(*)
            FROM chest_boosts cb
            WHERE cb.targetUserId = :userId AND cb.createdAt >= :sevenDaysAgo AND cb.boostType = 'referral'
          ) as weekBoostCount
        FROM users u
        WHERE u.id = :userId`,
            {
                replacements: {
                    userId,
                    today,
                    sevenDaysAgo
                },
                type: QueryTypes.SELECT,
            }
        );
        // 获取用户最近的加速记录（使用分页，只获取 referral 类型）
        const { count, rows: recentBoosts } = await ChestBoost.findAndCountAll({
            where: {
                targetUserId: userId,
                boostType: 'referral',
                createdAt: {
                    [Op.gte]: sevenDaysAgo
                }
            },
            order: [['createdAt', 'DESC']],
            limit: pageSize,
            offset: offset,
            include: [
                {
                    model: User,
                    as: 'sourceUser',
                    attributes: ['id', 'username', 'photoUrl']
                }
            ]
        });

        return {
            boostInfo: boostInfo[0],
            recentBoosts: {
                items: recentBoosts,
                total: count,
                page,
                pageSize,
                totalPages: Math.ceil(count / pageSize)
            }
        };
    } catch (error) {
        console.error("获取用户加速信息失败:", error);
        throw error;
    }
}


export async function verifyPaymentRequest(paymentId: string, telegramId: string) {
    const paymentInfo = await PaymentRequest.findOne({
        where: { paymentId }
    });

    if (!paymentInfo || paymentInfo.telegramId !== telegramId) {
        return null;
    }

    return paymentInfo;
}