// src/services/farmConfigOfflineRewardService.ts

import { UserWallet } from '../models/UserWallet';
import { FarmPlot } from '../models/FarmPlot';
import { FarmConfig } from '../models/FarmConfig';
import { WalletHistory } from '../models/WalletHistory';
import { sequelize } from '../config/db';
import { Transaction } from 'sequelize';
import { createBigNumber, formatToThreeDecimalsNumber } from '../utils/bigNumberConfig';

/**
 * 基于farm_configs配置表的离线奖励服务
 * 使用farm_configs表中的offline字段计算离线奖励
 */
export class FarmConfigOfflineRewardService {

  /**
   * 获取离线奖励信息（不实际发放）
   * @param walletId 钱包ID
   * @returns 离线奖励信息
   */
  static async getOfflineRewardInfo(walletId: number): Promise<{
    isOffline: boolean;
    offlineTime: number;
    offlineReward: {
      gem: number;
    };
    lastActiveTime: Date | null;
  }> {
    try {
      // 获取用户钱包信息
      const wallet = await UserWallet.findOne({
        where: { id: walletId }
      });

      if (!wallet) {
        throw new Error('用户钱包不存在');
      }

      const now = new Date();
      const lastActiveTime = wallet.lastActiveTime;
      
      // 如果用户从未活跃过，返回无奖励
      if (!lastActiveTime) {
        return {
          isOffline: false,
          offlineTime: 0,
          offlineReward: { gem: 0 },
          lastActiveTime: null
        };
      }

      // 计算离线时间（秒）
      const lastActiveDate = lastActiveTime instanceof Date ? lastActiveTime : new Date(lastActiveTime);
      const offlineTimeSeconds = Math.floor((now.getTime() - lastActiveDate.getTime()) / 1000);
      
      // 判断是否离线（超过2分钟）
      const isOffline = offlineTimeSeconds >= 120;
      
      if (!isOffline) {
        return {
          isOffline: false,
          offlineTime: offlineTimeSeconds,
          offlineReward: { gem: 0 },
          lastActiveTime: lastActiveDate
        };
      }

      // 计算离线奖励
      const offlineRewardGem = await this.calculateOfflineReward(walletId, offlineTimeSeconds);

      return {
        isOffline: true,
        offlineTime: offlineTimeSeconds,
        offlineReward: { gem: offlineRewardGem },
        lastActiveTime: lastActiveDate
      };

    } catch (error) {
      throw error;
    }
  }

  /**
   * 结算离线奖励（实际发放）
   * @param walletId 钱包ID
   * @param userId 用户ID
   * @param transaction 数据库事务
   * @returns 结算结果
   */
  static async claimOfflineReward(
    walletId: number, 
    userId: number, 
    transaction?: Transaction
  ): Promise<{
    claimedReward: {
      gem: number;
    };
    offlineTime: number;
    currentGem: number;
  }> {
    const useTransaction = transaction || await sequelize.transaction();
    const shouldCommit = !transaction;

    try {
      // 在事务中重新计算离线奖励信息，确保数据一致性
      const wallet = await UserWallet.findOne({
        where: { id: walletId },
        lock: true,
        transaction: useTransaction
      });

      if (!wallet) {
        throw new Error('用户钱包不存在');
      }

      const now = new Date();
      const lastActiveTime = wallet.lastActiveTime;

      // 如果用户从未活跃过，返回无奖励
      if (!lastActiveTime) {
        if (shouldCommit) await useTransaction.commit();
        return {
          claimedReward: { gem: 0 },
          offlineTime: 0,
          currentGem: formatToThreeDecimalsNumber(wallet.gem || 0)
        };
      }

      // 计算离线时间（秒）
      const lastActiveDate = lastActiveTime instanceof Date ? lastActiveTime : new Date(lastActiveTime);
      const offlineTimeSeconds = Math.floor((now.getTime() - lastActiveDate.getTime()) / 1000);

      // 判断是否离线（超过2分钟）
      const isOffline = offlineTimeSeconds >= 120;

      if (!isOffline) {
        if (shouldCommit) await useTransaction.commit();
        return {
          claimedReward: { gem: 0 },
          offlineTime: offlineTimeSeconds,
          currentGem: formatToThreeDecimalsNumber(wallet.gem || 0)
        };
      }

      // 计算离线奖励
      const offlineRewardGem = await this.calculateOfflineReward(walletId, offlineTimeSeconds);

      if (offlineRewardGem <= 0) {
        if (shouldCommit) await useTransaction.commit();
        return {
          claimedReward: { gem: 0 },
          offlineTime: offlineTimeSeconds,
          currentGem: formatToThreeDecimalsNumber(wallet.gem || 0)
        };
      }

      // 计算新的GEM余额
      const currentGemBN = createBigNumber(wallet.gem || 0);
      const rewardGemBN = createBigNumber(offlineRewardGem);
      const newGemBN = currentGemBN.plus(rewardGemBN);

      // 更新钱包
      wallet.gem = newGemBN.toFixed(3);
      wallet.lastActiveTime = new Date(); // 更新活跃时间
      await wallet.save({ transaction: useTransaction });

      // 创建钱包历史记录
      await WalletHistory.create({
        walletId,
        userId,
        currency: 'GEM',
        action: 'IN',
        amount: formatToThreeDecimalsNumber(offlineRewardGem),
        category: 'OFFLINE_REWARD',
        credit_type: 'OFFLINE_REWARD',
        reference: '离线奖励',
        fe_display_remark: `离线奖励获得 ${formatToThreeDecimalsNumber(offlineRewardGem)} 宝石`,
        developer_remark: `离线奖励获得宝石: ${formatToThreeDecimalsNumber(offlineRewardGem)}`
      }, { transaction: useTransaction });

      if (shouldCommit) await useTransaction.commit();

      return {
        claimedReward: { gem: formatToThreeDecimalsNumber(offlineRewardGem) },
        offlineTime: offlineTimeSeconds,
        currentGem: formatToThreeDecimalsNumber(newGemBN)
      };

    } catch (error) {
      if (shouldCommit) {
        try {
          await useTransaction.rollback();
        } catch (rollbackError) {
          // 忽略回滚错误
        }
      }
      throw error;
    }
  }

  /**
   * 计算离线奖励
   * @param walletId 钱包ID
   * @param offlineTimeSeconds 离线时间（秒）
   * @returns 离线奖励GEM数量
   */
  private static async calculateOfflineReward(walletId: number, offlineTimeSeconds: number): Promise<number> {
    try {
      // 限制最大离线时间为24小时
      const maxOfflineTimeSeconds = 24 * 60 * 60; // 24小时
      const effectiveOfflineTimeSeconds = Math.min(offlineTimeSeconds, maxOfflineTimeSeconds);
      
      // 转换为小时
      const offlineTimeHours = effectiveOfflineTimeSeconds / 3600;

      // 获取用户所有已解锁的农场区域
      const farmPlots = await FarmPlot.findAll({
        where: { 
          walletId, 
          isUnlocked: true 
        },
        order: [['plotNumber', 'ASC']]
      });

      if (farmPlots.length === 0) {
        return 0;
      }

      // 获取激活的农场配置
      const activeConfigs = await FarmConfig.getActiveConfigs();
      
      if (activeConfigs.length === 0) {
        throw new Error('没有找到激活的农场配置');
      }

      // 创建配置映射 grade -> config
      const configMap = new Map<number, FarmConfig>();
      activeConfigs.forEach(config => {
        configMap.set(config.grade, config);
      });

      let totalOfflineReward = createBigNumber(0);

      // 遍历每个已解锁的农场区域
      for (const farmPlot of farmPlots) {
        const config = configMap.get(farmPlot.level);
        
        if (!config) {
          console.warn(`农场区域 ${farmPlot.plotNumber} 等级 ${farmPlot.level} 没有找到对应配置`);
          continue;
        }

        // 计算该农场区域的离线收益
        // 离线收益 = offline字段值 × 离线时间（小时）
        const plotOfflineReward = createBigNumber(config.offline).multipliedBy(offlineTimeHours);
        totalOfflineReward = totalOfflineReward.plus(plotOfflineReward);
      }

      return formatToThreeDecimalsNumber(totalOfflineReward);

    } catch (error) {
      console.error('计算离线奖励失败:', error);
      throw error;
    }
  }
}
