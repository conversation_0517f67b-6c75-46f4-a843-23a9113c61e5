import axios, { AxiosInstance, AxiosResponse } from "axios";
import { AdjacentTransactionsResponse, Transaction } from "../types/types";
import { t } from "../i18n";

/**
 * 自定义错误类
 */
export class TransactionNotFoundError extends Error {
  constructor(message: string = t("errors.transactionNotFound")) {
    super(message);
    this.name = "TransactionNotFoundError";
  }
}

export class TonCenterAPIError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "TonCenterAPIError";
  }
}

class TonCenterClient {
  private axiosInstance: AxiosInstance;

  /**
   * 构造函数初始化 Axios 实例
   * @param apiKey TON Center 的 API 密钥
   * @param baseURL 可选的 API 基础 URL，默认为 'https://toncenter.com/api/v3'
   */
  constructor(
    private apiKey: string,
    baseURL: string = "https://toncenter.com/api/v3"
  ) {
    if (!apiKey) {
      throw new Error(t("errors.apiKeyRequired"));
    }

    this.axiosInstance = axios.create({
      baseURL,
      timeout: 5000,
      headers: {
        "X-API-Key": this.apiKey,
        "Content-Type": "application/json",
      },
    });
  }

  /**
   * 根据交易哈希获取交易信息
   * @param transactionHash 交易的哈希值
   * @returns 返回一个 Promise，解析为 Transaction 对象
   */
  public async getTransactionInfoByHash(
    transactionHash: string
  ): Promise<Transaction> {
    try {
      const response: AxiosResponse<AdjacentTransactionsResponse> =
        await this.axiosInstance.get("/adjacentTransactions", {
          params: {
            hash: transactionHash,
            direction: "both",
            limit: 1,
          },
        });

      if (response.data.transactions && response.data.transactions.length > 0) {
        return response.data.transactions[0];
      } else {
        throw new TransactionNotFoundError();
      }
    } catch (error) {
      if (error instanceof TransactionNotFoundError) {
        throw error;
      } else {
        console.error("Error fetching transaction info:", error);
        throw new TonCenterAPIError(
          t("errors.fetchTransactionInfoFailed")
        );
      }
    }
  }

  /**
   * 获取最新的区块哈希
   * @returns 返回一个 Promise，解析为最新的区块哈希
   */
  public async getLatestBlockNumber(): Promise<number> {
    try {
      const response: AxiosResponse = await this.axiosInstance.get("/blocks", {
        params: {
          limit: 1, // 只获取最新的一个区块
          sort: "desc", // 按时间降序
        },
      });
      // 提取并返回最新的区块的 root_hash
      if (response.data && response.data.blocks && response.data.blocks.length > 0) {
        const latestBlock = response.data.blocks[0];
        return latestBlock.seqno; // 返回 seqno 作为区块的哈希
      } else {
        throw new TonCenterAPIError(t("errors.noBlocksFound"));
      }
    } catch (error) {
      console.error("Error fetching latest block hash:", error);
      throw new TonCenterAPIError(t("errors.fetchLatestBlockHashFailed"));
    }
  }
}

export default TonCenterClient;
export type { Transaction };