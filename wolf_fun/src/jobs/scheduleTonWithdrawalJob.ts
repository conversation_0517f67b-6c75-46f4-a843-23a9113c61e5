// src/jobs/scheduleTonWithdrawalJob.ts
import { Queue } from "bullmq";
import IORedis from "ioredis";
import { tonWithdrawalQueue } from "./tonWithdrawalQueue";
import dotenv from "dotenv";

dotenv.config();

/**
 * 调度TON提现任务
 * 
 * 该函数负责创建一个定时任务，定期处理TON提现请求
 * 默认每5分钟执行一次，可通过环境变量配置
 */
export async function scheduleTonWithdrawalJob() {
  console.log('[ScheduleTonWithdrawal] 开始设置TON提现任务定时调度...');
  
  // 默认每1分钟执行一次，可通过环境变量配置
  // CRON表达式格式：秒 分 时 日 月 星期
  // "*/1 * * * *" 表示每1分钟执行一次
  const cronPattern = process.env.TON_WITHDRAWAL_SCHEDULE || "*/1 * * * *";
  const schedulerId = `scheduler-ton-withdrawal`;

  console.log(`[ScheduleTonWithdrawal] 正在设置任务: ${schedulerId}, CRON: ${cronPattern}`);
  
  try {
    await tonWithdrawalQueue.upsertJobScheduler(
      schedulerId,
      {
        pattern: cronPattern,
        immediately: true, // 是否在启动时立即执行一次
      },
      {
        name: `ton-withdrawal-queue`,
        data: {
          timestamp: new Date().toISOString(),
          type: 'ton-withdrawal'
        },
        opts: {
          backoff: 3,
          attempts: 5,
          removeOnFail: 1000,
          removeOnComplete: 1000,
        },
      }
    );
    console.log(`[ScheduleTonWithdrawal] TON提现任务设置成功: ${schedulerId}`);
  } catch (error) {
    console.error(`[ScheduleTonWithdrawal] 设置TON提现任务失败: ${schedulerId}`, error);
  }
}