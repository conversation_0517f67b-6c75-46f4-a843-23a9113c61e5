import cron from "node-cron";
import { IapPurchase, IapProduct } from "../models";
import { Op } from "sequelize";
import axios from "axios";
import { fulfillPurchase } from "../controllers/dappPortalPaymentController";

// Dapp Portal API 配置
const DAPP_PORTAL_BASE_URL = "https://payment.dappportal.io/api/payment-v1";
const CLIENT_ID = process.env.DAPP_PORTAL_CLIENT_ID;
const CLIENT_SECRET = process.env.DAPP_PORTAL_CLIENT_SECRET;

/**
 * 调用 Dapp Portal API 获取支付状态
 */
async function getPaymentStatus(paymentId: string) {
  try {
    const response = await axios.get(`${DAPP_PORTAL_BASE_URL}/payment/status`, {
      params: { id: paymentId },
      headers: {
        'X-Client-Id': CLIENT_ID,
        'X-Client-Secret': CLIENT_SECRET,
        'Content-Type': 'application/json'
      }
    });
    return response.data;
  } catch (error: any) {
    console.error(`[PaymentStatusUpdater] 获取支付状态失败 (paymentId: ${paymentId}):`, error.response?.data || error.message);
    throw error;
  }
}



/**
 * 处理单个支付记录的状态更新
 */
async function updatePurchaseStatus(purchase: any) {
  try {
    console.log(`[PaymentStatusUpdater] 检查支付状态: ${purchase.paymentId}`);
    
    // 调用 DappPortal API 获取最新状态
    const statusResponse = await getPaymentStatus(purchase.paymentId);
    const dappPortalStatus = statusResponse.status;
    
    console.log(`[PaymentStatusUpdater] PaymentId: ${purchase.paymentId}, DappPortal状态: ${dappPortalStatus}, 当前数据库状态: ${purchase.status}`);
    
    // 直接使用 Dapp Portal 的状态
    const validStatuses = ['CREATED', 'STARTED', 'REGISTERED_ON_PG', 'CAPTURED', 'PENDING', 'CONFIRMED', 'FINALIZED', 'REFUNDED', 'CONFIRM_FAILED', 'CANCELED', 'CHARGEBACK'];
    const finalStatus = validStatuses.includes(dappPortalStatus) ? dappPortalStatus : 'CREATED';
    
    // 标记为已检查
    await purchase.update({ statusChecked: true });
    
    // 如果状态有变化，更新数据库
    if (purchase.status !== finalStatus) {
      console.log(`[PaymentStatusUpdater] 状态变化: ${purchase.status} -> ${finalStatus}`);
      
      await purchase.update({ status: finalStatus });
      
      // 只有在状态为 FINALIZED 时才发放商品
      if (finalStatus === 'FINALIZED') {
        try {
          await fulfillPurchase(purchase);
          console.log(`[PaymentStatusUpdater] 商品发放成功: ${purchase.paymentId}`);
        } catch (fulfillError) {
          console.error(`[PaymentStatusUpdater] 商品发放失败: ${purchase.paymentId}`, fulfillError);
        }
      }
    } else {
      console.log(`[PaymentStatusUpdater] 状态无变化: ${finalStatus}`);
    }
    
  } catch (error) {
    console.error(`[PaymentStatusUpdater] 处理支付记录失败: ${purchase.paymentId}`, error);
    
    // 即使失败也标记为已检查，避免重复处理
    try {
      await purchase.update({ statusChecked: true });
    } catch (updateError) {
      console.error(`[PaymentStatusUpdater] 更新statusChecked失败: ${purchase.paymentId}`, updateError);
    }
  }
}

/**
 * 批量更新支付状态
 */
export async function updatePaymentStatuses() {
  try {
    console.log('[PaymentStatusUpdater] 开始检查支付状态...');
    
    // 计算5分钟前的时间
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
    
    // 查找所有未完成且未检查过状态的支付记录（创建时间超过5分钟）
    const pendingPurchases = await IapPurchase.findAll({
      where: {
        statusChecked: false, // 只检查未检查过的记录
        createdAt: {
          [Op.lte]: fiveMinutesAgo // 大于等于5分钟的订单
        }
      },
      include: [{ model: IapProduct }],
      limit: 50, // 限制每次处理的数量，避免API调用过多
      order: [['createdAt', 'ASC']] // 优先处理较早的记录
    });
    
    if (pendingPurchases.length === 0) {
      console.log('[PaymentStatusUpdater] 没有需要检查的支付记录');
      return;
    }
    
    console.log(`[PaymentStatusUpdater] 找到 ${pendingPurchases.length} 条待检查的支付记录`);
    
    // 逐个处理支付记录
    for (const purchase of pendingPurchases) {
      await updatePurchaseStatus(purchase);
      
      // 添加小延迟，避免API调用过于频繁
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    console.log('[PaymentStatusUpdater] 支付状态检查完成');
    
  } catch (error) {
    console.error('[PaymentStatusUpdater] 批量更新支付状态失败:', error);
  }
}

/**
 * 启动支付状态更新定时任务
 */
export function startPaymentStatusUpdater() {
  // 每5分钟执行一次
  const task = cron.schedule('*/5 * * * *', async () => {
    await updatePaymentStatuses();
  }, {
    scheduled: false // 不立即启动
  });
  
  console.log('[PaymentStatusUpdater] 支付状态更新定时任务已创建，每5分钟执行一次');
  
  return task;
}

/**
 * 停止支付状态更新定时任务
 */
export function stopPaymentStatusUpdater(task: cron.ScheduledTask) {
  if (task) {
    task.stop();
    console.log('[PaymentStatusUpdater] 支付状态更新定时任务已停止');
  }
}