// 严格验证批量资源更新接口的完整功能测试
const axios = require('axios');

const BASE_URL = 'http://localhost:3456';

// 测试配置
const TEST_CONFIG = {
  // 这里需要一个有效的JWT token
  // 可以通过以下方式获取：
  // 1. 从浏览器开发者工具中复制
  // 2. 通过登录接口获取
  // 3. 使用现有的测试token
  JWT_TOKEN: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsIndhbGxldElkIjoxLCJpYXQiOjE3MzQ1MzE2NzIsImV4cCI6MTczNDYxODA3Mn0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8', // 请替换为有效token
  
  // 测试用例配置
  testCases: [
    {
      name: '正常请求 - 小量资源',
      request: {
        gemRequest: 2.000,
        milkOperations: {
          produce: 5.000,
          consume: 3.000
        }
      },
      expectedValidation: true,
      description: '请求合理数量的资源，应该通过严格验证'
    },
    {
      name: '牛奶产量轻微超限',
      request: {
        gemRequest: 5.000,
        milkOperations: {
          produce: 100.000, // 可能超限
          consume: 5.000
        }
      },
      expectedValidation: false,
      description: '牛奶产量可能超出1.5倍容错范围'
    },
    {
      name: '牛奶消耗轻微超限',
      request: {
        gemRequest: 5.000,
        milkOperations: {
          produce: 10.000,
          consume: 100.000 // 可能超限
        }
      },
      expectedValidation: false,
      description: '牛奶消耗可能超出1.5倍容错范围'
    },
    {
      name: '宝石增加超限',
      request: {
        gemRequest: 500.000, // 明显超限
        milkOperations: {
          produce: 10.000,
          consume: 5.000
        }
      },
      expectedValidation: false,
      description: '宝石增加量明显超出转换汇率限制'
    },
    {
      name: '多项验证失败',
      request: {
        gemRequest: 1000.000,
        milkOperations: {
          produce: 1000.000,
          consume: 1000.000
        }
      },
      expectedValidation: false,
      description: '所有参数都超限，应该触发多项验证失败'
    },
    {
      name: '只请求GEM',
      request: {
        gemRequest: 3.000
      },
      expectedValidation: true,
      description: '只请求少量GEM，应该通过验证'
    },
    {
      name: '只请求牛奶操作',
      request: {
        milkOperations: {
          produce: 8.000,
          consume: 4.000
        }
      },
      expectedValidation: true,
      description: '只请求牛奶操作，应该通过验证'
    },
    {
      name: '边界值测试',
      request: {
        gemRequest: 0.001,
        milkOperations: {
          produce: 0.001,
          consume: 0.001
        }
      },
      expectedValidation: true,
      description: '极小值测试，应该通过验证'
    }
  ]
};

// 请求配置
const getRequestConfig = (token) => ({
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  }
});

// 验证token有效性
async function validateToken(token) {
  try {
    console.log('🔍 验证JWT token有效性...');
    const response = await axios.get(`${BASE_URL}/api/test/health`, getRequestConfig(token));
    
    if (response.status === 200) {
      console.log('✅ Token验证成功');
      return true;
    }
  } catch (error) {
    if (error.response?.status === 401) {
      console.log('❌ Token无效或已过期');
      console.log('💡 请更新TEST_CONFIG.JWT_TOKEN为有效的token');
      return false;
    } else {
      console.log('⚠️  Token验证请求失败:', error.message);
      return false;
    }
  }
  return false;
}

// 获取基准数据
async function getBaselineData(token) {
  try {
    console.log('📊 获取基准数据...');
    
    // 调用旧接口获取基准数据
    const response = await axios.post(
      `${BASE_URL}/api/wallet/batch-update-resources`,
      {
        gemRequest: 0.001,
        milkOperations: {
          produce: 0.001,
          consume: 0.001
        }
      },
      getRequestConfig(token)
    );
    
    if (response.data.ok) {
      const data = response.data.data;
      console.log('✅ 基准数据获取成功');
      console.log(`   时间间隔: ${data.changes.productionRates.timeElapsedSeconds}秒`);
      console.log(`   农场产量: ${data.changes.productionRates.farmMilkPerCycle}`);
      console.log(`   出货单位: ${data.changes.productionRates.deliveryBlockUnit}`);
      console.log(`   出货价格: ${data.changes.productionRates.deliveryBlockPrice}`);
      
      return {
        timeElapsed: data.changes.productionRates.timeElapsedSeconds,
        farmMilkPerCycle: data.changes.productionRates.farmMilkPerCycle,
        deliveryBlockUnit: data.changes.productionRates.deliveryBlockUnit,
        deliveryBlockPrice: data.changes.productionRates.deliveryBlockPrice
      };
    }
  } catch (error) {
    console.log('⚠️  基准数据获取失败:', error.response?.data?.message || error.message);
  }
  
  return null;
}

// 执行单个测试用例
async function runTestCase(testCase, index, total, token) {
  console.log(`\n🧪 测试 ${index + 1}/${total}: ${testCase.name}`);
  console.log('-'.repeat(60));
  console.log(`📝 描述: ${testCase.description}`);
  console.log(`📋 请求参数:`, JSON.stringify(testCase.request, null, 2));
  
  try {
    const response = await axios.post(
      `${BASE_URL}/api/wallet/strict-batch-update-resources`,
      testCase.request,
      getRequestConfig(token)
    );
    
    if (response.data.ok) {
      const data = response.data.data;
      const changes = data.changes;
      
      console.log('✅ 请求成功');
      console.log(`   消息: ${response.data.message}`);
      console.log(`   使用严格验证: ${changes.usedStrictValidation ? '是' : '否'}`);
      console.log(`   验证通过: ${changes.validationPassed ? '是' : '否'}`);
      console.log(`   回退到旧方法: ${changes.fallbackToOldMethod ? '是' : '否'}`);
      
      // 验证结果分析
      const actualValidation = changes.validationPassed;
      if (actualValidation === testCase.expectedValidation) {
        console.log(`✅ 验证结果符合预期: ${actualValidation ? '通过' : '失败'}`);
      } else {
        console.log(`❌ 验证结果不符合预期: 期望${testCase.expectedValidation ? '通过' : '失败'}，实际${actualValidation ? '通过' : '失败'}`);
      }
      
      // 显示验证详情
      if (changes.strictValidationDetails) {
        const details = changes.strictValidationDetails;
        console.log('   📊 验证详情:');
        console.log(`     牛奶产量验证: ${details.milkProductionValid ? '✅' : '❌'}`);
        console.log(`     牛奶消耗验证: ${details.milkConsumptionValid ? '✅' : '❌'}`);
        console.log(`     宝石转换验证: ${details.gemConversionValid ? '✅' : '❌'}`);
        
        if (details.reason) {
          console.log(`     失败原因: ${details.reason}`);
        }
        
        // 显示具体数值对比
        console.log('   🔢 数值对比:');
        const milkProd = details.validationDetails.milkProduction;
        const milkCons = details.validationDetails.milkConsumption;
        const gemConv = details.validationDetails.gemConversion;
        
        console.log(`     牛奶产量: 请求${milkProd.requested.toFixed(3)} vs 允许${milkProd.maxAllowed.toFixed(3)} (理论${milkProd.calculated.toFixed(3)})`);
        console.log(`     牛奶消耗: 请求${milkCons.requested.toFixed(3)} vs 允许${milkCons.maxAllowed.toFixed(3)} (理论${milkCons.calculated.toFixed(3)})`);
        console.log(`     宝石转换: 请求${gemConv.requested.toFixed(3)} vs 允许${gemConv.maxAllowed.toFixed(3)} (汇率${gemConv.conversionRate.toFixed(3)})`);
      }
      
      // 显示资源变化
      console.log('   💰 资源变化:');
      console.log(`     GEM: ${data.beforeUpdate.gem} → ${data.afterUpdate.gem} (${changes.details.gem.increased > 0 ? '+' : ''}${changes.details.gem.increased})`);
      console.log(`     牛奶: ${data.beforeUpdate.pendingMilk} → ${data.afterUpdate.pendingMilk} (+${changes.details.milk.increased} -${changes.details.milk.decreased})`);
      
      return {
        success: true,
        validationPassed: actualValidation,
        expectedValidation: testCase.expectedValidation,
        matchesExpectation: actualValidation === testCase.expectedValidation
      };
      
    } else {
      console.log('❌ 请求失败');
      console.log(`   错误: ${response.data.message}`);
      return { success: false };
    }
    
  } catch (error) {
    console.log('❌ 请求异常');
    if (error.response) {
      console.log(`   状态码: ${error.response.status}`);
      console.log(`   错误信息: ${error.response.data.message || error.response.data.error}`);
    } else {
      console.log(`   错误: ${error.message}`);
    }
    return { success: false, error: error.message };
  }
}

// 主测试函数
async function runComprehensiveTest() {
  console.log('🚀 开始严格验证批量资源更新接口的完整功能测试');
  console.log('='.repeat(80));
  
  // 验证token
  const isTokenValid = await validateToken(TEST_CONFIG.JWT_TOKEN);
  if (!isTokenValid) {
    console.log('\n❌ 测试终止：JWT token无效');
    console.log('请更新TEST_CONFIG.JWT_TOKEN为有效的token后重新运行测试');
    return;
  }
  
  // 获取基准数据
  const baseline = await getBaselineData(TEST_CONFIG.JWT_TOKEN);
  if (baseline) {
    console.log('📈 基准数据将用于分析验证逻辑的合理性');
  }
  
  console.log('\n' + '='.repeat(80));
  console.log('开始执行测试用例...');
  
  const results = [];
  
  // 执行所有测试用例
  for (let i = 0; i < TEST_CONFIG.testCases.length; i++) {
    const testCase = TEST_CONFIG.testCases[i];
    const result = await runTestCase(testCase, i, TEST_CONFIG.testCases.length, TEST_CONFIG.JWT_TOKEN);
    results.push({ testCase, result });
    
    // 测试间隔，避免频率限制
    if (i < TEST_CONFIG.testCases.length - 1) {
      console.log('⏳ 等待6秒避免频率限制...');
      await new Promise(resolve => setTimeout(resolve, 6000));
    }
  }
  
  // 测试结果汇总
  console.log('\n' + '='.repeat(80));
  console.log('📊 测试结果汇总');
  console.log('='.repeat(80));
  
  const successCount = results.filter(r => r.result.success).length;
  const matchCount = results.filter(r => r.result.matchesExpectation).length;
  
  console.log(`总测试用例: ${results.length}`);
  console.log(`成功执行: ${successCount}`);
  console.log(`符合预期: ${matchCount}`);
  console.log(`成功率: ${(successCount / results.length * 100).toFixed(1)}%`);
  console.log(`准确率: ${(matchCount / results.length * 100).toFixed(1)}%`);
  
  // 详细结果
  console.log('\n📋 详细结果:');
  results.forEach((item, index) => {
    const { testCase, result } = item;
    const status = result.success ? 
      (result.matchesExpectation ? '✅' : '⚠️') : '❌';
    console.log(`${index + 1}. ${status} ${testCase.name}`);
    if (!result.success) {
      console.log(`   错误: ${result.error || '请求失败'}`);
    } else if (!result.matchesExpectation) {
      console.log(`   预期: ${testCase.expectedValidation ? '通过' : '失败'}, 实际: ${result.validationPassed ? '通过' : '失败'}`);
    }
  });
  
  console.log('\n🎉 测试完成！');
  
  if (matchCount === results.length) {
    console.log('🎊 所有测试用例都符合预期，严格验证逻辑工作正常！');
  } else {
    console.log('⚠️  部分测试用例结果与预期不符，可能需要调整验证逻辑或测试用例');
  }
}

// 运行测试
runComprehensiveTest().catch(console.error);
