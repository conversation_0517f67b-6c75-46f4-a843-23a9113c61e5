---
description: 
globs: 
alwaysApply: false
---
# 开发工作流程指南

## 开发环境设置
1. 安装依赖: `npm install` 或 `pnpm install`
2. 配置环境变量: 复制并编辑 `.env` 文件
3. 数据库迁移: `npm run seed:tasks`
4. 启动开发服务器: `npm run dev`

## 脚本命令
- `npm run dev` - 开发模式启动 (监听文件变化)
- `npm run build` - 生产环境构建
- `npm run build:dev` - 开发环境构建  
- `npm run start` - 生产环境启动
- `npm run start:dev` - 开发环境启动
- `npm run seed:tasks` - 数据库迁移和种子数据

## 代码组织原则
1. **分层架构**: Routes → Controllers → Services → Models
2. **单一职责**: 每个文件只处理一个功能模块
3. **依赖注入**: 通过参数传递依赖，避免直接导入
4. **错误处理**: 统一的错误处理机制

## 添加新功能流程
1. 在 `src/models/` 中定义数据模型
2. 在 `migrations/` 中创建数据库迁移
3. 在 `src/controllers/` 中实现业务逻辑
4. 在 `src/routes/` 中定义API路由
5. 在 [src/app.ts](mdc:src/app.ts) 中注册路由
6. 添加相应的类型定义和DTO

## 数据库操作
- 模型定义: [src/models/index.ts](mdc:src/models/index.ts)
- 迁移文件: `migrations/` 目录
- 种子数据: `seeders/` 目录
- 配置文件: [config/config.js](mdc:config/config.js)

## 部署
- 本地部署: [docker-compose.yml](mdc:docker-compose.yml)
- 生产部署: [deploy.sh](mdc:deploy.sh)
- Bot部署: [deploy-bot.sh](mdc:deploy-bot.sh)
