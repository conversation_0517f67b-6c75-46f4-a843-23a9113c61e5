
services:
  redis:
    image: redis:6
    container_name: redis6-moofun
    restart: always
    ports:
      - "6258:6379"
    privileged: true
    volumes:
      - ./redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - moofun

  redisinsight:
    image: redislabs/redisinsight:latest
    restart: always
    container_name: redisinsight-moofun
    ports:
      - "5578:5540"
    depends_on:
      - redis
    volumes:
      - ./redis-insight:/db
    networks:
      - moofun

  mysql:
    image: mysql:8.3.0
    container_name: mysql-8.3.0-moofun
    environment:
      MYSQL_ROOT_PASSWORD: 00321zixun
      MYSQL_DATABASE: moofun
      MYSQL_USER: moofun
      MYSQL_PASSWORD: 00321zixunadmin
      TZ: Asia/Shanghai
    volumes:
      - ./mysql-data:/var/lib/mysql
      - ./mysqld.cnf:/etc/mysql/conf.d/mysqld.cnf
    ports:
      - "3670:3306"
    networks:
      - moofun
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u$$MYSQL_USER", "-p$$MYSQL_PASSWORD"]
      interval: 10s
      timeout: 5s
      retries: 5
  
  phpmyadmin:
    image: phpmyadmin:5.2.1
    container_name: phpmyadmin5.2.1-moofun
    depends_on:
      mysql:
        condition: service_healthy
    environment:
      PMA_ARBITRARY: 1
      PMA_HOST: mysql
    ports:
      - "8270:80"
    volumes:
      - ./uploads.ini:/usr/local/etc/php/conf.d/uploads.ini:ro
    networks:
      - moofun

networks:
  moofun:
    name: moofun
    driver: bridge