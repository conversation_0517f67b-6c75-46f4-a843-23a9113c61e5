# 农场区块新配置更新文档

## 概述

本次更新将农场区块的计算方式从基于数学公式的动态计算改为基于预设数组的查表方式，提供更精确的游戏平衡控制。

## 更新内容

### 1. 新增配置文件

**文件**: `src/config/farmPlotConfig.ts`

包含以下配置数据：
- `FARM_PLOT_BARN_COUNT`: 牛舍数量配置 (20个等级)
- `FARM_PLOT_PRODUCTION_SPEED`: 生产速度配置 (20个等级)
- `FARM_PLOT_UNLOCK_COST`: 解锁费用配置 (20个农场区块)
- `FARM_PLOT_MILK_PRODUCTION`: 牛奶产量配置 (20个农场区块 × 20个等级)
- `FARM_PLOT_UPGRADE_COST`: 升级费用配置 (20个农场区块 × 20个等级)

### 2. 更新的计算类

**文件**: `src/utils/bigNumberConfig.ts`

`FarmPlotCalculator` 类的所有方法已更新为使用新的配置数据：
- `calculateBaseProduction()`: 使用预设产量数组
- `calculateProductionSpeed()`: 使用预设速度数组
- `calculateBarnCount()`: 使用预设牛舍数量数组
- `calculateUpgradeCostByLevel()`: 使用预设升级费用数组
- `calculateUnlockCost()`: 使用预设解锁费用数组

### 3. 更新的服务文件

以下服务文件已更新以使用新配置：
- `src/services/farmPlotService.ts`: 农场区块服务
- `src/services/batchResourceUpdateService.ts`: 批量资源更新服务
- `src/services/testResetService.ts`: 测试重置服务

### 4. 更新的模型文件

**文件**: `src/models/FarmPlot.ts`

农场区块模型的升级方法已更新为使用新配置数据。

### 5. 数据库迁移

**文件**: `migrations/20250703000000-update-farm-plot-new-config.js`

提供数据库迁移脚本，将现有农场区块数据更新为新配置值。

## 配置数据特点

### 牛舍数量
- 等级1-20: 1, 2, 3, ..., 20 (线性增长)

### 生产速度 (秒/次)
- 等级1: 5.0秒
- 等级20: 1.88秒
- 递减模式，等级越高速度越快

### 解锁费用
- 农场区块1: 0 (免费)
- 农场区块2: 378,250
- 农场区块20: 395,385,000
- 非线性增长，后期农场区块解锁费用显著增加

### 牛奶产量
- 农场区块1等级1: 1.0
- 农场区块2等级1: 1.5
- 农场区块20等级20: 14.7
- 每个农场区块有独立的产量进度表

### 升级费用
- 农场区块1等级1: 100
- 农场区块2等级1: 9,493
- 农场区块20等级20: 43,084,354
- 每个农场区块有独立的升级费用进度表

## 主要变化

### 从公式计算到查表
**旧方式**:
```typescript
// 基础产量 = 1 × (2.0)^(编号-1) × (1.5)^(等级-1)
const baseProduction = Math.pow(2.0, plotNumber - 1) * Math.pow(1.5, level - 1);
```

**新方式**:
```typescript
// 直接从预设数组查表
const baseProduction = getFarmPlotMilkProduction(plotNumber, level);
```

### 更精确的游戏平衡
- 每个农场区块和等级的数值都经过精心设计
- 避免了数学公式可能产生的不合理数值
- 提供更好的游戏体验和平衡性

## 兼容性

### API响应格式
- 所有API响应格式保持不变
- `nextUpgradeGrowth` 字段继续提供升级预览数据
- 数值精度仍为3位小数

### VIP和Speed Boost效果
- VIP和Speed Boost效果的计算逻辑保持不变
- 新配置数据与现有加成系统完全兼容

## 部署步骤

1. **代码部署**: 部署包含新配置的代码
2. **数据库迁移**: 运行迁移脚本更新现有数据
3. **验证**: 确认所有农场区块数据正确更新
4. **测试**: 验证升级、解锁等功能正常工作

## 回滚方案

如需回滚，可以使用迁移脚本的 `down` 方法，将数据恢复到基于公式的计算方式。

## 注意事项

1. **数据一致性**: 迁移过程中会自动更新所有现有农场区块数据
2. **性能提升**: 查表方式比公式计算更高效
3. **维护性**: 游戏平衡调整只需修改配置数组，无需改动计算逻辑
4. **扩展性**: 可以轻松添加新的农场区块或等级配置

## 测试验证

已通过以下测试验证：
- ✅ 配置数据结构完整性
- ✅ 关键数据点正确性
- ✅ API响应格式兼容性
- ✅ 升级预览功能正常
- ✅ VIP和Speed Boost效果正常

## 总结

本次更新成功将农场区块系统从公式驱动改为配置驱动，提供了：
- 更精确的游戏平衡控制
- 更好的性能表现
- 更易于维护和调整的系统架构
- 完全向后兼容的API接口

新配置已准备就绪，可以安全部署到生产环境。
