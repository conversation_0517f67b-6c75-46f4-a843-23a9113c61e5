# IAP 产品配置字段说明

## 概述

为了更好地支持不同类型的 IAP 产品，我们引入了 `config` 字段来存储产品特定的配置信息。这样可以避免为不同类型的产品使用不适用的字段。

## 字段结构

### 基础道具 (speed_boost, time_warp)

对于基础道具，仍然使用原有的 `multiplier`、`duration`、`quantity` 字段：

```json
{
  "type": "speed_boost",
  "multiplier": 2,
  "duration": 24,
  "quantity": 1
}
```

### VIP 会员 (vip_membership)

VIP 会员使用 `config` 字段存储特定配置：

```json
{
  "type": "vip_membership",
  "config": {
    "durationDays": 30,
    "deliverySpeedBonus": 0.3,
    "blockPriceBonus": 0.2,
    "productionSpeedBonus": 0.3,
    "benefits": ["delivery_speed_boost", "block_price_boost", "production_speed_boost"]
  }
}
```

**字段说明：**
- `durationDays`: VIP 会员持续天数
- `deliverySpeedBonus`: 出货线速度加成比例（0.3 = 30%）
- `blockPriceBonus`: 出货线价格加成比例（0.2 = 20%）
- `productionSpeedBonus`: 牧场区生产速度加成比例（0.3 = 30%）
- `benefits`: 会员权益列表

### 特殊套餐 (special_offer)

特殊套餐使用 `config` 字段定义套餐内容：

```json
{
  "type": "special_offer",
  "config": {
    "bundle": [
      {
        "type": "time_warp",
        "multiplier": 1,
        "duration": 24,
        "quantity": 2,
        "autoUse": true
      },
      {
        "type": "speed_boost",
        "multiplier": 4,
        "duration": 24,
        "quantity": 2,
        "autoUse": false
      }
    ]
  }
}
```

**字段说明：**
- `bundle`: 套餐包含的道具列表
  - `type`: 道具类型
  - `multiplier`: 倍数（对于加速道具）
  - `duration`: 持续时间（小时）
  - `quantity`: 数量
  - `autoUse`: 是否自动使用（true=购买后直接使用，false=添加到背包）

## 使用方式

### 在种子数据中

```javascript
{
  productId: 'vip_membership_30d',
  name: 'VIP Membership',
  type: 'vip_membership',
  config: JSON.stringify({
    durationDays: 30,
    deliverySpeedBonus: 0.3,
    blockPriceBonus: 0.2,
    productionSpeedBonus: 0.3,
    benefits: ['delivery_speed_boost', 'block_price_boost', 'production_speed_boost']
  })
}
```

### 在代码中访问

```typescript
const product = await IapProduct.findByPk(productId);
const config = product.config || {};

// VIP 会员
if (product.type === 'vip_membership') {
  const durationDays = config.durationDays || 30;
  const deliverySpeedBonus = config.deliverySpeedBonus || 0;
  const blockPriceBonus = config.blockPriceBonus || 0;
  const productionSpeedBonus = config.productionSpeedBonus || 0;
}

// 特殊套餐
if (product.type === 'special_offer') {
  const bundle = config.bundle || [];
  for (const item of bundle) {
    const autoUse = item.autoUse || false;
    if (item.type === 'time_warp' && autoUse) {
      // 时间跳跃道具将直接使用
    }
  }
}
```

## 优势

1. **类型安全**: 每种产品类型都有明确的配置结构
2. **扩展性**: 可以轻松添加新的配置字段而不影响现有数据
3. **清晰性**: 避免了不适用字段的混淆（如 VIP 会员的 multiplier）
4. **灵活性**: 特殊套餐可以包含任意组合的道具

## 迁移说明

1. 添加了 `config` 字段到 `iap_products` 表
2. 更新了 `IapProduct` 模型以支持 `config` 字段
3. 修改了种子数据以使用新的配置结构
4. 更新了 `IapController` 以正确处理不同类型的产品配置 