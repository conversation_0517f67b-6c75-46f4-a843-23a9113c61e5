# 分享助力链接 API

### 获取分享助力链接

**接口路径**：`GET /api/jackpot-chest/share-links`

**接口描述**：获取用户的分享助力链接列表，包括链接ID、分享码、宝箱等级、当前使用次数、最大使用次数、过期时间、剩余有效时间和无法使用原因等信息。默认只返回未过期且未达到最大使用次数的链接。

**请求方法**：GET

**认证要求**：需要钱包认证（walletAuthMiddleware）

**请求参数**：

| 参数名 | 类型 | 必填 | 描述 |
|-------|-----|------|------|
| status | String | 否 | 链接状态过滤，可选值：'all'（全部）, 'used'（已使用）, 'unused'（未使用），默认为'unused' |

**响应结果**：

```json
{
  "ok": true,
  "data": [
    {
      "id": 123,
      "code": "abc123",
      "chestLevel": 2,
      "currentUses": 3,
      "maxUses": 5,
      "expiresAt": "2023-01-01T00:00:00.000Z",
      "remainingTime": 86400000,
      "unusableReason": null
    }
    // 更多链接...
  ]
}
```

**响应字段说明**：

| 字段名 | 类型 | 描述 |
|-------|-----|------|
| id | Number | 分享链接ID |
| code | String | 分享链接的唯一码，用于助力 |
| chestLevel | Number | 宝箱等级 |
| currentUses | Number | 当前已使用次数 |
| maxUses | Number | 最大可使用次数 |
| expiresAt | String | 过期时间，ISO格式的日期字符串 |
| remainingTime | Number | 剩余有效时间（毫秒） |
| unusableReason | String | 链接无法使用的原因，如果链接可用则为null。可能的值：链接已过期、链接已达到最大使用次数 |

**错误响应**：

```json
{
  "ok": false,
  "message": "获取用户分享助力链接失败"
}
```

**错误码**：

| HTTP状态码 | 错误信息 | 描述 |
|-----------|---------|------|
| 500 | 服务器错误 | 服务器内部错误 |
| 401 | 未授权 | 用户未登录或token无效 |