# 免费门票转账 API

## 转账免费门票

### 接口信息

- **路径**: `/api/free-ticket/transfer`
- **方法**: POST
- **描述**: 将免费门票从当前用户钱包转账给指定钱包地址
- **认证**: 需要钱包认证，请求头中必须包含 `Authorization: Bearer <token>`

### 请求参数

#### 请求头

| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| Authorization | string | 是 | Bearer 认证token |

#### 请求体

```json
{
  "toWalletAddress": "EQA...",  // 接收方钱包地址
  "amount": 10                  // 转账数量，必须为正整数
}
```

| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| toWalletAddress | string | 是 | 接收方钱包地址 |
| amount | integer | 是 | 转账数量，必须为正整数 |

### 响应参数

#### 成功响应

```json
{
	"ok": true,
	"data": {
		"fromWalletId": 1,
		"toWalletAddress": "UQBhQxkk_fkznrqzwOAwrNJlHdp1el2f-AJoa1UBMUz-ERGb",
		"amount": 1,
		"timestamp": "2025-04-07T15:24:10.426Z"
	},
	"message": "Free tickets transferred successfully"
}
```

| 参数名 | 类型 | 描述 |
| ------ | ---- | ---- |
| fromWalletId | integer | 发送方钱包ID |
| toWalletAddress | string | 接收方钱包地址 |
| amount | integer | 转账数量 |
| timestamp | string | 转账时间戳 |

