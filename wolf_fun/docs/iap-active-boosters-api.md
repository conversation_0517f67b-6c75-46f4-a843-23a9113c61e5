# IAP 激活道具查询 API

## 接口信息

- **路径**: `/api/iap/boosters/active`
- **方法**: GET
- **描述**: 获取用户当前激活的道具列表，只返回未过期的激活道具
- **认证**: 需要钱包认证，请求头中必须包含 `Authorization: Bearer <token>`

## 请求参数

### 请求头

| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| Authorization | string | 是 | Bearer 认证token |

### 查询参数

无需查询参数

## 响应参数

### 成功响应

**状态码**: 200

```json
{
  "ok": true,
  "activeBoosters": [
    {
      "id": 1,
      "walletId": 123,
      "productId": 1,
      "type": "speed_boost",
      "multiplier": 2,
      "startTime": "2024-01-15T10:30:00.000Z",
      "endTime": "2024-01-16T10:30:00.000Z",
      "status": "active",
      "createdAt": "2024-01-15T10:30:00.000Z",
      "updatedAt": "2024-01-15T10:30:00.000Z",
      "product": {
        "id": 1,
        "name": "2倍速度提升道具",
        "description": "提升生产速度2倍，持续24小时",
        "type": "speed_boost",
        "priceUsd": 4.99,
        "priceKaia": 100,
        "config": {
          "effectType": "production_speed",
          "stackable": false
        }
      }
    },
    {
      "id": 2,
      "walletId": 123,
      "productId": 3,
      "type": "time_warp",
      "multiplier": 1,
      "startTime": "2024-01-15T12:00:00.000Z",
      "endTime": "2024-01-15T16:00:00.000Z",
      "status": "active",
      "createdAt": "2024-01-15T12:00:00.000Z",
      "updatedAt": "2024-01-15T12:00:00.000Z",
      "product": {
        "id": 3,
        "name": "4小时时间跳跃",
        "description": "立即获得4小时的产出",
        "type": "time_warp",
        "priceUsd": 2.99,
        "priceKaia": 60,
        "config": {
          "effectType": "instant_production",
          "stackable": true
        }
      }
    }
  ]
}
```

### 响应字段说明

| 字段名 | 类型 | 描述 |
| ------ | ---- | ---- |
| ok | boolean | 操作是否成功 |
| activeBoosters | array | 激活道具列表 |
| activeBoosters[].id | number | 激活道具记录ID |
| activeBoosters[].walletId | number | 钱包ID |
| activeBoosters[].productId | number | 关联的商品ID |
| activeBoosters[].type | string | 道具类型，可能值: `speed_boost`(速度提升), `time_warp`(时间跳跃) |
| activeBoosters[].multiplier | number | 加成倍数 |
| activeBoosters[].startTime | string | 激活开始时间 |
| activeBoosters[].endTime | string | 激活结束时间 |
| activeBoosters[].status | string | 状态，值为 `active` |
| activeBoosters[].product | object | 关联的商品信息 |
| activeBoosters[].product.id | number | 商品ID |
| activeBoosters[].product.name | string | 商品名称 |
| activeBoosters[].product.description | string | 商品描述 |
| activeBoosters[].product.type | string | 商品类型 |
| activeBoosters[].product.priceUsd | number | USD价格 |
| activeBoosters[].product.priceKaia | number | KAIA价格 |
| activeBoosters[].product.config | object | 商品配置信息 |

### 错误响应

#### 钱包ID缺失 (400)

```json
{
  "error": "Wallet ID is required"
}
```

#### 服务器错误 (500)

```json
{
  "error": "Internal server error"
}
```

## 业务逻辑

### 查询条件

1. **用户过滤**: 只返回当前用户的激活道具
2. **时间过滤**: 只返回 `endTime` 大于当前时间的道具
3. **排序**: 按照 `endTime` 升序排列，即将过期的道具排在前面

### 道具类型说明

- **speed_boost**: 速度提升道具，提升生产速度
- **time_warp**: 时间跳跃道具，立即获得一定时间的产出

### 状态说明

- **active**: 道具正在生效中
- **expired**: 道具已过期（此接口不会返回）
- **used**: 道具已使用完毕（此接口不会返回）

## 使用示例

### 获取激活道具列表

```bash
curl -X GET http://your-domain/api/iap/boosters/active \
  -H "Authorization: Bearer your-token" \
  -H "Content-Type: application/json"
```

### 响应示例（无激活道具）

```json
{
  "ok": true,
  "activeBoosters": []
}
```

## 注意事项

1. **认证要求**: 所有请求必须包含有效的Bearer token
2. **时间过滤**: 接口自动过滤已过期的道具，只返回当前有效的激活道具
3. **排序规则**: 结果按结束时间升序排列，方便用户查看即将过期的道具
4. **关联数据**: 包含完整的商品信息，便于前端展示道具详情
5. **实时性**: 查询结果基于当前时间实时计算，确保返回的都是有效道具

## 相关接口

- [获取用户道具](/api/iap/boosters) - 查看用户拥有的所有道具
- [使用道具](/api/iap/boosters/use) - 激活道具效果
- [获取商店商品列表](/api/iap/store/products) - 购买新的道具
- [创建支付订单](/api/iap/payment/create) - 购买道具