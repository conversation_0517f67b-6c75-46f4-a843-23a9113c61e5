# 重置宝箱倒计时 API 文档

本文档描述了用于重置宝箱倒计时的API接口。该接口仅用于测试环境，可以帮助开发人员和测试人员更方便地测试宝箱相关功能。

## 基本信息

- 基础路径: `/api/test-chest`
- 所有接口都需要用户身份验证（钱包认证和语言中间件）

## API 接口详情

### 重置宝箱倒计时

该接口用于将用户的宝箱倒计时重置为当前时间，使宝箱立即可领取，便于测试宝箱领取功能。

**请求方法**: POST

**路径**: `/api/test-chest/reset-chest-countdown`

**请求头**:

| 参数名 | 类型 | 必填 | 描述 |
| --- | --- | --- | --- |
| Authorization | String | 是 | 用户认证令牌，格式为 `Bearer {token}` |

**请求参数**: 无需额外参数

**请求示例**:

```http
POST /api/test-chest/reset-chest-countdown HTTP/1.1
Host: api.example.com
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json
```

**响应示例**:

```json
{
  "ok": true,
  "message": "宝箱倒计时已重置，现在可以立即领取",
  "data": {
    "userId": 123,
    "walletId": 456,
    "nextAvailableTime": "2023-06-15T08:30:00.000Z"
  }
}
```

**错误响应**:

```json
{
  "ok": false,
  "message": "未找到倒计时记录"
}
```

## 使用场景

该接口主要用于以下测试场景：

1. **测试宝箱领取功能**：当用户需要等待倒计时结束才能领取下一个宝箱时，可以使用此接口重置倒计时，立即进行宝箱领取测试。

2. **测试宝箱奖励机制**：快速重置倒计时，连续领取多个宝箱，测试奖励分配机制。

3. **UI/UX测试**：测试宝箱可领取状态下的界面展示和交互效果。

## 注意事项

- 此接口仅在测试环境中可用，生产环境中不应启用。
- 接口调用成功后，用户可以立即领取新的宝箱，无需等待原有的倒计时结束。
- 此操作不会影响用户已有的宝箱数量和状态，只会修改下次可领取宝箱的时间。