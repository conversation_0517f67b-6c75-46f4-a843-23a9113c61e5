# IAP VIP状态查询 API

## 接口信息

- **路径**: `/api/iap/vip/status`
- **方法**: GET
- **描述**: 获取用户的VIP会员状态信息
- **认证**: 需要钱包认证，请求头中必须包含 `Authorization: Bearer <token>`

## 请求参数

### Headers

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| Authorization | string | 是 | Bearer token，用于用户认证 |

### Query参数

无需额外的查询参数

## 响应参数

### 成功响应

**状态码**: 200

#### 用户有VIP会员

```json
{
  "ok": true,
  "isVip": true,
  "membership": {
    "id": 1,
    "walletId": 123,
    "startTime": "2024-01-15T10:30:00.000Z",
    "endTime": "2024-02-15T10:30:00.000Z",
    "isActive": true,
    "createdAt": "2024-01-15T10:30:00.000Z",
    "updatedAt": "2024-01-15T10:30:00.000Z"
  }
}
```

#### 用户无VIP会员

```json
{
  "ok": true,
  "isVip": false,
  "membership": null
}
```

### 响应字段说明

| 字段名 | 类型 | 描述 |
|--------|------|------|
| ok | boolean | 请求是否成功 |
| isVip | boolean | 用户是否为VIP会员 |
| membership | object\|null | VIP会员信息，如果用户不是VIP则为null |

#### membership对象字段说明

| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | number | VIP会员记录ID |
| walletId | number | 用户钱包ID |
| startTime | string | VIP开始时间（ISO 8601格式） |
| endTime | string | VIP结束时间（ISO 8601格式） |
| isActive | boolean | VIP是否当前激活状态 |
| createdAt | string | 记录创建时间（ISO 8601格式） |
| updatedAt | string | 记录更新时间（ISO 8601格式） |

### 错误响应

#### 钱包ID缺失 (400)

```json
{
  "error": "Wallet ID is required"
}
```

#### 认证失败 (401)

```json
{
  "error": "Unauthorized"
}
```

#### 服务器内部错误 (500)

```json
{
  "error": "Internal server error"
}
```

## 业务逻辑说明

### VIP状态检查

1. **自动状态更新**: 接口会自动检查VIP会员的有效期，如果当前时间超出了VIP有效期，会自动将`isActive`状态更新为`false`
2. **时间验证**: 系统会比较当前时间与`startTime`和`endTime`，确保VIP状态的准确性
3. **唯一性**: 每个用户钱包只能有一个VIP会员记录

### VIP权益

根据IAP产品配置，VIP会员享有以下权益：
- 30%出货线速度加成
- 20%出货线价格加成  
- 30%牧场区生产速度加成

## 使用示例

### 请求示例

```bash
curl -X GET http://your-domain/api/iap/vip/status \
  -H "Authorization: Bearer your-token"
```

### 响应示例

```json
{
  "ok": true,
  "isVip": true,
  "membership": {
    "id": 1,
    "walletId": 123,
    "startTime": "2024-01-15T10:30:00.000Z",
    "endTime": "2024-02-15T10:30:00.000Z",
    "isActive": true,
    "createdAt": "2024-01-15T10:30:00.000Z",
    "updatedAt": "2024-01-15T10:30:00.000Z"
  }
}
```

## 注意事项

1. **认证要求**: 所有请求必须包含有效的Bearer token
2. **状态实时性**: 接口会实时检查并更新VIP状态，确保返回的信息准确
3. **时区处理**: 所有时间字段均为UTC时间，前端需要根据用户时区进行转换
4. **缓存建议**: 由于VIP状态变化不频繁，建议前端适当缓存结果以提升性能

## 相关接口

- [获取商店商品列表](/api/iap/store/products) - 购买VIP会员
- [创建支付订单](/api/iap/payment/create) - 购买VIP会员
- [获取购买历史](/api/iap/purchase/history) - 查看VIP购买记录
- [获取用户道具](/api/iap/boosters) - 查看其他道具