# 接口文档：解锁牧场区

## 1. 概述

该接口用于解锁用户指定的牧场区。解锁会消耗用户的 GEM，并使牧场区变为可用状态，同时设置其初始等级、牛舍数量、产量等属性。

## 2. 请求

-   **URL**: `/api/farm/farm-plots/unlock`
-   **方法**: `POST`
-   **认证**: 需要用户认证 

## 3. 请求体 (Request Body)

-   **Content-Type**: `application/json`

| 参数名      | 类型   | 是否必需 | 描述             | 示例        |
| ----------- | ------ | -------- | ---------------- | ----------- |
| `plotNumber` | number | 是       | 要解锁的牧场区编号 | `3`         |

**示例请求体:**

```json
{
  "plotNumber": 3
}
```

## 4. 响应 (Response)

### 4.1. 成功响应

-   **状态码**: `200 OK`
-   **Content-Type**: `application/json`

**响应体结构:**

```json
{
  "ok": true,
  "data": {
    "id": "integer",
    "walletId": "integer",
    "plotNumber": "integer",
    "level": "integer", // 解锁后默认为 1
    "barnCount": "integer", // 解锁后默认为 1
    "milkProduction": "number", // 解锁后的初始单次产奶量 (3位小数精度)
    "productionSpeed": "number", // 解锁后的初始生产速度 (秒/次, 3位小数精度)
    "unlockCost": "number", // 该牧场区的解锁费用
    "upgradeCost": "number", // 解锁后，升到下一级所需的GEM
    "lastProductionTime": "string(date-time)",
    "isUnlocked": "boolean", // 解锁后为 true
    "accumulatedMilk": "number", // 解锁时通常为 0
    "createdAt": "string(date-time)",
    "updatedAt": "string(date-time)",
    "baseProduction": "number", // 基础产量 (3位小数精度)
    "nextUpgradeGrowth": {
      "nextProductionSpeed": "number", // 下次升级后的生产速度 (3位小数精度)
      "nextBarnCount": "integer", // 下次升级后的牛舍数量
      "nextMilkProduction": "number" // 下次升级后的产量 (3位小数精度)
    }
  }
}
```

**响应字段说明:**

-   `id`: 牧场区记录的唯一ID
-   `walletId`: 用户钱包ID
-   `plotNumber`: 牧场区编号 (1-20)
-   `level`: 解锁后的牧场区等级 (默认为1)
-   `barnCount`: 解锁后的牛舍数量 (默认为1)
-   `milkProduction`: 解锁后，牧场区单次生产的牛奶量 (3位小数精度)
-   `productionSpeed`: 解锁后，牧场区的生产速度（单位：秒/次，3位小数精度）
-   `unlockCost`: 解锁该牧场区实际消耗的GEM
-   `upgradeCost`: 解锁并初始化后，升级到下一等级所需的GEM数量
-   `lastProductionTime`: 上次产奶的时间戳 (解锁时会初始化)
-   `isUnlocked`: 牧场区是否已解锁 (此接口成功后为 `true`)
-   `accumulatedMilk`: 牧场区当前累积的牛奶量 (解锁时通常为0)
-   `createdAt`: 牧场区记录创建时间
-   `updatedAt`: 牧场区记录最后更新时间
-   `baseProduction`: 基础产量 (3位小数精度)
-   `nextUpgradeGrowth`: 下次升级预览数据对象，包含：
    -   `nextProductionSpeed`: 下次升级后的生产速度 (3位小数精度)
    -   `nextBarnCount`: 下次升级后的牛舍数量
    -   `nextMilkProduction`: 下次升级后的产量 (3位小数精度)

**示例成功响应:**

```json
{
  "ok": true,
  "data": {
    "id": 2,
    "walletId": 1,
    "plotNumber": 2,
    "level": 1,
    "barnCount": 1,
    "milkProduction": 1.000,
    "productionSpeed": 5.000,
    "unlockCost": 2000,
    "upgradeCost": 200,
    "lastProductionTime": "2025-06-18T07:17:29.000Z",
    "isUnlocked": true,
    "accumulatedMilk": 0,
    "createdAt": "2025-06-18 07:17:29",
    "updatedAt": "2025-06-18T07:17:29.000Z",
    "baseProduction": 1.000,
    "nextUpgradeGrowth": {
      "nextProductionSpeed": 4.762,
      "nextBarnCount": 2,
      "nextMilkProduction": 1.500
    }
  }
}
```

### 4.2. 错误响应

-   **状态码**: `400 Bad Request` 或 `500 Internal Server Error`
-   **Content-Type**: `application/json`

**常见错误情况:**

```json
{
  "ok": false,
  "error": "用户钱包或牧场区不存在"
}
```

```json
{
  "ok": false,
  "error": "牧场区已解锁"
}
```

```json
{
  "ok": false,
  "error": "请先解锁前一个牧场区"
}
```

```json
{
  "ok": false,
  "error": "GEM不足"
}
```

## 5. 解锁公式说明

### 5.1. 解锁计算公式

-   **解锁费用**: 编号1免费，编号2开始 = 2000 × 2^(编号-2)
-   **初始产量**: 1.000 (所有农场区解锁后的初始产量相同)
-   **初始生产速度**: 5.000秒 (所有农场区解锁后的初始速度相同)
-   **初始牛舍数量**: 1个
-   **初始升级费用**: 200 GEM

### 5.2. nextUpgradeGrowth 说明

解锁成功后，响应中的 `nextUpgradeGrowth` 对象包含首次升级的预览数据：
-   `nextProductionSpeed`: 升级到2级后的生产速度 (4.762秒)
-   `nextBarnCount`: 升级到2级后的牛舍数量 (2个)
-   `nextMilkProduction`: 升级到2级后的产量 (1.500)

### 5.3. 解锁费用参考表

| 农场区编号 | 解锁费用 (GEM) |
| --- | --- |
| 1 | 0 (免费) |
| 2 | 2,000 |
| 3 | 4,000 |
| 4 | 8,000 |
| 5 | 16,000 |
| 6 | 32,000 |
| 7 | 64,000 |
| 8 | 128,000 |
| 9 | 256,000 |
| 10 | 512,000 |

## 6. 注意事项

-   解锁牧场区会消耗用户钱包中的 `GEM` 货币
-   必须按顺序解锁牧场区（不能跳过前面的农场区）
-   农场区编号1默认解锁，无需调用此接口
-   解锁后的农场区初始等级为1，可以立即开始升级
-   所有数值使用BigNumber.js计算，确保3位小数精度
-   解锁后立即返回包含 `nextUpgradeGrowth` 的完整数据，前端无需额外API调用