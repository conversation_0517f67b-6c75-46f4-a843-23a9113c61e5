# IAP 用户道具查询 API
## 接口信息

- **路径**: `/api/iap/boosters`
- **方法**: GET
- **描述**: 获取用户拥有的道具
- **认证**: 需要钱包认证，请求头中必须包含 `Authorization: Bearer <token>`

## 请求参数
### 请求头

| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| Authorization | string | 是 | Bearer 认证token |

### 查询参数

无需查询参数

## 响应参数
### 成功响应

**状态码**: 200

```json
{
  "success": true,
  "boosters": [
    {
      "id": 1,
      "walletId": 123,
      "type": "speed_boost",
      "multiplier": 2,
      "duration": 24,
      "quantity": 3,
      "createdAt": "2024-01-15T10:30:00.000Z",
      "updatedAt": "2024-01-15T10:30:00.000Z",
      "product": {
        "id": 1,
        "productId": "speed_boost_2x_24h",
        "name": "2倍速度提升道具",
        "type": "speed_boost",
        "multiplier": 2,
        "duration": 24,
        "description": "提升生产速度2倍，持续24小时"
      }
    },
    {
      "id": 2,
      "walletId": 123,
      "type": "time_warp",
      "multiplier": 1,
      "duration": 4,
      "quantity": 2,
      "createdAt": "2024-01-16T10:30:00.000Z",
      "updatedAt": "2024-01-16T10:30:00.000Z",
      "product": {
        "id": 3,
        "productId": "time_warp_4h",
        "name": "4小时时间跳跃",
        "type": "time_warp",
        "multiplier": 1,
        "duration": 4,
        "description": "立即获得4小时的产出"
      }
    }
  ]
}
```

## 字段说明

### boosters 数组字段

| 字段名 | 类型 | 描述 |
| ------ | ---- | ---- |
| id | number | 道具ID |
| walletId | number | 用户钱包ID |
| type | string | 道具类型，可能值: `speed_boost`(速度提升), `time_warp`(时间跳跃) |
| multiplier | number | 加成倍数，如2倍、4倍 |
| duration | number | 持续时间（小时） |
| quantity | number | 拥有数量 |
| createdAt | string | 创建时间 |
| updatedAt | string | 更新时间 |
| product | object | 关联的商品信息，可能为null |

### product 对象字段

| 字段名 | 类型 | 描述 |
| ------ | ---- | ---- |
| id | number | 商品ID |
| productId | string | 商品唯一标识 |
| name | string | 商品名称 |
| type | string | 商品类型 |
| multiplier | number | 加成倍数（对于速度提升道具） |
| duration | number | 持续时间（小时） |
| description | string | 商品描述 |

## 业务逻辑说明

1. 接口会返回用户拥有的所有道具（数量大于0）
2. 同时返回当前激活状态的道具（结束时间大于当前时间）
3. 每个道具会关联对应的商品信息，包含名称、描述等详细信息
4. 如果道具没有对应的商品信息，product字段将为null