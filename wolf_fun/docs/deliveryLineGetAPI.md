# 接口文档：获取用户出货线

## 1. 概述

该接口用于获取当前认证用户的出货线信息。如果用户尚无出货线记录，系统会自动为其初始化一条新的出货线并返回。


## 2. 请求

-   **URL**: `/api/delivery/delivery-line`
-   **方法**: `GET`
-   **认证**: 需要用户认证

## 3. 请求参数 (Query Parameters)

无

## 4. 响应 (Response)

### 4.1. 成功响应

-   **状态码**: `200 OK`
-   **Content-Type**: `application/json`

**响应体结构:**

```json
{
  "ok": true,
  "data": {
    "id": "integer",
    "walletId": "integer",
    "level": "integer",
    "deliverySpeed": "number", // 出货速度 (秒/次, 3位小数精度)
    "blockUnit": "number", // 每个牛奶方块包含的牛奶数量 (3位小数精度)
    "blockPrice": "number", // 每个牛奶方块的价格 (GEM, 3位小数精度)
    "upgradeCost": "number", // 升级到下一等级所需的GEM
    "lastDeliveryTime": "string(date-time)", // 上次出货时间
    "pendingMilk": "number", // 待处理的牛奶数量
    "createdAt": "string(date-time)",
    "updatedAt": "string(date-time)",
    "hasBoost": "boolean", // 是否有加成效果
    "boostMultiplier": "number", // 加成倍数
    "nextUpgradeGrowth": {
      "nextDeliverySpeed": "number", // 下次升级后的出货速度 (3位小数精度)
      "nextBlockUnit": "number", // 下次升级后的方块容量 (3位小数精度)
      "nextBlockPrice": "number" // 下次升级后的方块价格 (3位小数精度)
    }
  }
}
```

**响应字段说明:**

-   `id`: 出货线记录的唯一ID
-   `walletId`: 用户钱包ID
-   `level`: 出货线的当前等级
-   `deliverySpeed`: 出货速度（单位：秒/次，3位小数精度）。数值越小，速度越快
-   `blockUnit`: 每个牛奶方块包含的牛奶数量（3位小数精度）
-   `blockPrice`: 每个牛奶方块出售时能获得的GEM数量（3位小数精度）
-   `upgradeCost`: 升级到下一等级出货线所需的GEM数量
-   `lastDeliveryTime`: 上一次完成出货的时间戳
-   `pendingMilk`: 已经添加到出货线但尚未转换成牛奶方块的牛奶数量
-   `createdAt`: 出货线记录创建时间
-   `updatedAt`: 出货线记录最后更新时间
-   `hasBoost`: 是否有加成效果（VIP或道具加成）
-   `boostMultiplier`: 加成倍数
-   `nextUpgradeGrowth`: 下次升级预览数据对象，包含：
    -   `nextDeliverySpeed`: 下次升级后的出货速度（3位小数精度）
    -   `nextBlockUnit`: 下次升级后的方块容量（3位小数精度）
    -   `nextBlockPrice`: 下次升级后的方块价格（3位小数精度）

**示例成功响应:**

```json
{
  "ok": true,
  "data": {
    "id": 1,
    "walletId": 1,
    "level": 2,
    "deliverySpeed": 4.950,
    "blockUnit": 10.000,
    "blockPrice": 10.000,
    "upgradeCost": 1000,
    "lastDeliveryTime": "2025-06-18T10:00:00.000Z",
    "pendingMilk": 15,
    "createdAt": "2025-06-18 07:17:29",
    "updatedAt": "2025-06-18T10:05:00.000Z",
    "hasBoost": false,
    "boostMultiplier": 1,
    "nextUpgradeGrowth": {
      "nextDeliverySpeed": 4.901,
      "nextBlockUnit": 20.000,
      "nextBlockPrice": 20.000
    }
  }
}
```

### 4.2. 错误响应

-   **状态码**: `400 Bad Request` 或 `500 Internal Server Error`
-   **Content-Type**: `application/json`

**常见错误情况:**

```json
{
  "ok": false,
  "error": "用户钱包或出货线不存在"
}
```

## 5. 升级公式说明

### 5.1. 出货线升级计算公式

-   **出货速度**: 每次升级提升1%，新速度 = 当前速度 ÷ 1.01
-   **方块容量**: 每次升级提升2倍，新容量 = 当前容量 × 2.0
-   **方块价格**: 每次升级提升2倍，新价格 = 当前价格 × 2.0
-   **升级费用**: 每次升级提升2倍，新费用 = 当前费用 × 2.0

### 5.2. 初始值说明

-   **初始等级**: 1
-   **初始出货速度**: 5.000秒
-   **初始方块容量**: 5.000牛奶/方块
-   **初始方块价格**: 5.000 GEM/方块
-   **初始升级费用**: 500 GEM

### 5.3. nextUpgradeGrowth 说明

`nextUpgradeGrowth` 对象包含下次升级后的预览数据：
-   `nextDeliverySpeed`: 升级后的出货速度
-   `nextBlockUnit`: 升级后的方块容量
-   `nextBlockPrice`: 升级后的方块价格

### 5.4. 加成系统说明

-   `hasBoost`: 表示是否有VIP或道具加成效果
-   `boostMultiplier`: 加成倍数，影响出货速度和方块价格
-   VIP用户享有30%的出货速度加成和20%的方块价格加成
-   Speed Boost道具提供额外的速度加成

## 6. 注意事项

-   如果用户尚无出货线记录，系统会自动初始化一条新的出货线
-   所有数值使用BigNumber.js计算，确保3位小数精度
-   `pendingBlocks` 字段已从API响应中移除，前端可通过 `Math.floor(pendingMilk / blockUnit)` 计算
-   加成效果会实时应用到 `deliverySpeed` 和 `blockPrice` 字段
-   `nextUpgradeGrowth` 提供升级预览，帮助用户决策是否升级
