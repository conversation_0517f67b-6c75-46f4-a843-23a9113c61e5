// 测试出货线加成效果的正确应用
// 验证VIP会员和Speed Boost道具的加成计算

console.log('=== 出货线加成效果测试 ===\n');

// 基础出货线数据
const baseDeliveryLine = {
  id: 1,
  walletId: 1,
  level: 2,
  deliverySpeed: 4.950,  // 基础出货速度
  blockUnit: 10.000,     // 基础方块容量
  blockPrice: 10.000,    // 基础方块价格
  upgradeCost: 1000,
  pendingMilk: 15
};

// VIP效果
const vipEffects = {
  isVip: true,
  deliverySpeedMultiplier: 1.3,  // 30% 出货线速度加成
  blockPriceMultiplier: 1.2,     // 20% 出货线价格加成
  productionSpeedMultiplier: 1.3  // 30% 牧场区生产速度加成
};

// Speed Boost道具效果
const speedBoostEffects = {
  x2_1hr: { speedMultiplier: 3.0 },   // +200% 速度加成
  x2_12hr: { speedMultiplier: 3.0 },  // +200% 速度加成
  x4_24hr: { speedMultiplier: 5.0 }   // +400% 速度加成
};

// 测试函数：计算加成后的出货线数据
function calculateBoostedDeliveryLine(baseData, vipEffects, boosterEffects) {
  // 计算总的出货线速度加成倍率
  const deliverySpeedMultiplier = vipEffects.deliverySpeedMultiplier * boosterEffects.speedMultiplier;
  
  // 计算出货线价格加成倍率（只有VIP提供20%的价格加成）
  const blockPriceMultiplier = vipEffects.blockPriceMultiplier;
  
  // 应用加成效果
  const boostedDeliverySpeed = baseData.deliverySpeed / deliverySpeedMultiplier;
  const boostedBlockPrice = baseData.blockPrice * blockPriceMultiplier;
  
  return {
    ...baseData,
    deliverySpeed: Number(boostedDeliverySpeed.toFixed(3)),
    blockPrice: Number(boostedBlockPrice.toFixed(3)),
    hasBoost: deliverySpeedMultiplier > 1 || blockPriceMultiplier > 1,
    boostMultiplier: deliverySpeedMultiplier,
    // 计算下次升级预览（也应用加成）
    nextUpgradeGrowth: calculateNextUpgradeGrowthWithBoosts(baseData, deliverySpeedMultiplier, blockPriceMultiplier)
  };
}

// 计算下次升级预览（带加成）
function calculateNextUpgradeGrowthWithBoosts(deliveryLine, deliverySpeedMultiplier, blockPriceMultiplier) {
  // 升级公式
  const baseNextDeliverySpeed = deliveryLine.deliverySpeed / 1.01;  // 提升1%速度
  const baseNextBlockUnit = deliveryLine.blockUnit * 2.0;           // 提升2倍容量
  const baseNextBlockPrice = deliveryLine.blockPrice * 2.0;         // 提升2倍价格
  
  // 应用加成效果
  const nextDeliverySpeed = baseNextDeliverySpeed / deliverySpeedMultiplier;
  const nextBlockUnit = baseNextBlockUnit;  // 容量不受加成影响
  const nextBlockPrice = baseNextBlockPrice * blockPriceMultiplier;
  
  return {
    nextDeliverySpeed: Number(nextDeliverySpeed.toFixed(3)),
    nextBlockUnit: Number(nextBlockUnit.toFixed(3)),
    nextBlockPrice: Number(nextBlockPrice.toFixed(3))
  };
}

// 测试不同加成组合
function testBoostCombinations() {
  console.log('🧪 测试不同加成组合\n');
  
  const testCases = [
    {
      name: '无加成',
      vip: { deliverySpeedMultiplier: 1, blockPriceMultiplier: 1 },
      booster: { speedMultiplier: 1 }
    },
    {
      name: '仅VIP会员',
      vip: vipEffects,
      booster: { speedMultiplier: 1 }
    },
    {
      name: 'VIP + Speed Boost x2',
      vip: vipEffects,
      booster: speedBoostEffects.x2_1hr
    },
    {
      name: 'VIP + Speed Boost x4',
      vip: vipEffects,
      booster: speedBoostEffects.x4_24hr
    },
    {
      name: '仅Speed Boost x2',
      vip: { deliverySpeedMultiplier: 1, blockPriceMultiplier: 1 },
      booster: speedBoostEffects.x2_1hr
    },
    {
      name: '仅Speed Boost x4',
      vip: { deliverySpeedMultiplier: 1, blockPriceMultiplier: 1 },
      booster: speedBoostEffects.x4_24hr
    }
  ];
  
  testCases.forEach(testCase => {
    console.log(`📊 ${testCase.name}:`);
    console.log('=====================================');
    
    const result = calculateBoostedDeliveryLine(baseDeliveryLine, testCase.vip, testCase.booster);
    
    const totalSpeedMultiplier = testCase.vip.deliverySpeedMultiplier * testCase.booster.speedMultiplier;
    const speedImprovement = ((baseDeliveryLine.deliverySpeed - result.deliverySpeed) / baseDeliveryLine.deliverySpeed * 100).toFixed(1);
    const priceImprovement = ((result.blockPrice - baseDeliveryLine.blockPrice) / baseDeliveryLine.blockPrice * 100).toFixed(1);
    
    console.log(`基础出货速度: ${baseDeliveryLine.deliverySpeed}秒`);
    console.log(`加成后速度: ${result.deliverySpeed}秒`);
    console.log(`速度提升: ${speedImprovement}% (倍率: ${totalSpeedMultiplier}x)`);
    console.log('');
    console.log(`基础方块价格: ${baseDeliveryLine.blockPrice} GEM`);
    console.log(`加成后价格: ${result.blockPrice} GEM`);
    console.log(`价格提升: ${priceImprovement}% (倍率: ${testCase.vip.blockPriceMultiplier}x)`);
    console.log('');
    console.log('下次升级预览:');
    console.log(`  速度: ${result.nextUpgradeGrowth.nextDeliverySpeed}秒`);
    console.log(`  容量: ${result.nextUpgradeGrowth.nextBlockUnit}牛奶/方块`);
    console.log(`  价格: ${result.nextUpgradeGrowth.nextBlockPrice} GEM/方块`);
    console.log('');
  });
}

// 测试Speed Boost道具效果
function testSpeedBoostEffects() {
  console.log('🚀 Speed Boost道具效果测试\n');
  
  const speedBoosts = [
    { name: 'Speed Boost x2 1hr', price: '$0.99', multiplier: 3.0, description: '+200% 出货线速度加成' },
    { name: 'Speed Boost x2 12hr', price: '$3.99', multiplier: 3.0, description: '+200% 出货线速度加成' },
    { name: 'Speed Boost x4 24hr', price: '$7.99', multiplier: 5.0, description: '+400% 出货线速度加成' }
  ];
  
  speedBoosts.forEach(boost => {
    console.log(`📦 ${boost.name} (${boost.price}):`);
    console.log(`${boost.description}`);
    
    const baseSpeed = 4.950;
    const boostedSpeed = baseSpeed / boost.multiplier;
    const improvement = ((baseSpeed - boostedSpeed) / baseSpeed * 100).toFixed(1);
    
    console.log(`基础速度: ${baseSpeed}秒`);
    console.log(`加成后速度: ${boostedSpeed.toFixed(3)}秒`);
    console.log(`速度提升: ${improvement}%`);
    console.log('');
  });
}

// 测试VIP会员效果
function testVipEffects() {
  console.log('👑 VIP会员效果测试\n');
  
  console.log('VIP会员加成:');
  console.log('- 出货线送货速度增加30%');
  console.log('- 出货线方块价格增加20%');
  console.log('- 牧场区生产速度增加30%');
  console.log('');
  
  const baseSpeed = 4.950;
  const basePrice = 10.000;
  
  const vipSpeed = baseSpeed / 1.3;
  const vipPrice = basePrice * 1.2;
  
  console.log('出货线效果:');
  console.log(`基础出货速度: ${baseSpeed}秒`);
  console.log(`VIP出货速度: ${vipSpeed.toFixed(3)}秒`);
  console.log(`速度提升: ${((baseSpeed - vipSpeed) / baseSpeed * 100).toFixed(1)}%`);
  console.log('');
  console.log(`基础方块价格: ${basePrice} GEM`);
  console.log(`VIP方块价格: ${vipPrice.toFixed(3)} GEM`);
  console.log(`价格提升: ${((vipPrice - basePrice) / basePrice * 100).toFixed(1)}%`);
  console.log('');
}

// 测试API响应格式
function testApiResponseFormat() {
  console.log('📋 API响应格式测试\n');
  
  // 模拟VIP + Speed Boost x2的情况
  const result = calculateBoostedDeliveryLine(
    baseDeliveryLine, 
    vipEffects, 
    speedBoostEffects.x2_1hr
  );
  
  console.log('API响应示例 (VIP + Speed Boost x2):');
  console.log(JSON.stringify({
    ok: true,
    data: result
  }, null, 2));
  
  console.log('\n字段说明:');
  console.log('✅ deliverySpeed: 应用加成后的实际出货速度');
  console.log('✅ blockPrice: 应用VIP加成后的实际方块价格');
  console.log('✅ hasBoost: 是否有任何加成效果');
  console.log('✅ boostMultiplier: 总的速度加成倍数');
  console.log('✅ nextUpgradeGrowth: 考虑当前加成的升级预览');
  console.log('');
}

// 测试加成计算的准确性
function testBoostCalculationAccuracy() {
  console.log('🎯 加成计算准确性测试\n');
  
  console.log('验证加成公式:');
  console.log('- 出货速度: 实际速度 = 基础速度 ÷ (VIP倍数 × 道具倍数)');
  console.log('- 方块价格: 实际价格 = 基础价格 × VIP价格倍数');
  console.log('');
  
  const testCases = [
    {
      name: 'VIP (1.3x) + Speed Boost x2 (3.0x)',
      vipSpeed: 1.3,
      vipPrice: 1.2,
      boostSpeed: 3.0,
      expectedSpeedMultiplier: 1.3 * 3.0,
      expectedPriceMultiplier: 1.2
    },
    {
      name: 'VIP (1.3x) + Speed Boost x4 (5.0x)',
      vipSpeed: 1.3,
      vipPrice: 1.2,
      boostSpeed: 5.0,
      expectedSpeedMultiplier: 1.3 * 5.0,
      expectedPriceMultiplier: 1.2
    }
  ];
  
  testCases.forEach(testCase => {
    console.log(`🧮 ${testCase.name}:`);
    
    const baseSpeed = 4.950;
    const basePrice = 10.000;
    
    const calculatedSpeed = baseSpeed / testCase.expectedSpeedMultiplier;
    const calculatedPrice = basePrice * testCase.expectedPriceMultiplier;
    
    console.log(`基础速度: ${baseSpeed}秒`);
    console.log(`计算速度: ${calculatedSpeed.toFixed(3)}秒`);
    console.log(`速度倍数: ${testCase.expectedSpeedMultiplier}x`);
    console.log('');
    console.log(`基础价格: ${basePrice} GEM`);
    console.log(`计算价格: ${calculatedPrice.toFixed(3)} GEM`);
    console.log(`价格倍数: ${testCase.expectedPriceMultiplier}x`);
    console.log('');
  });
}

// 运行所有测试
console.log('开始测试出货线加成效果...\n');

testVipEffects();
testSpeedBoostEffects();
testBoostCombinations();
testApiResponseFormat();
testBoostCalculationAccuracy();

console.log('🎉 所有测试完成！');
console.log('');
console.log('📝 总结:');
console.log('=====================================');
console.log('✅ VIP会员提供30%出货速度和20%价格加成');
console.log('✅ Speed Boost x2提供200%速度加成 (3.0x倍数)');
console.log('✅ Speed Boost x4提供400%速度加成 (5.0x倍数)');
console.log('✅ 加成效果可以叠加 (VIP × 道具)');
console.log('✅ API响应包含应用加成后的实际数值');
console.log('✅ nextUpgradeGrowth考虑当前加成效果');

console.log('\n=== 测试完成 ===');
