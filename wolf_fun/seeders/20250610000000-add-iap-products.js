'use strict';
const axios = require('axios');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // 检查是否已存在数据，如果存在则跳过
    const existingProducts = await queryInterface.sequelize.query(
      'SELECT COUNT(*) as count FROM iap_products',
      { type: Sequelize.QueryTypes.SELECT }
    );
    
    if (existingProducts[0].count > 0) {
      console.log('IAP products already exist, skipping seed data insertion');
      return;
    }
    
    // 获取当前Kaia价格
    console.log('正在从Kaiascan API获取Kaia价格...');
    let kaiaUsdPrice;
    try {
      const KAIASCAN_API_URL = 'https://mainnet-oapi.kaiascan.io/api?module=stats&action=coinprice&apikey=625b664e-c431-442c-9648-b4a9bb5d3a3c';
      const response = await axios.get(KAIASCAN_API_URL, {
        timeout: 10000, // 10秒超时
      });
      
      if (response.data && response.data.status === '1' && response.data.result) {
        kaiaUsdPrice = parseFloat(response.data.result.coin_usd);
        if (isNaN(kaiaUsdPrice) || kaiaUsdPrice <= 0) {
          throw new Error(`无效的价格数据: ${response.data.result.coin_usd}`);
        }
        console.log(`获取到Kaia价格: 1 KAIA = ${kaiaUsdPrice} USD`);
      } else {
        throw new Error(`API返回错误状态: ${response.data?.status || 'unknown'}`);
      }
    } catch (error) {
      console.error('获取Kaia价格失败:', error);
      console.log('使用默认价格 0.114 USD/KAIA');
      kaiaUsdPrice = 0.114; // 默认价格，如果API调用失败
    }

    await queryInterface.bulkInsert('iap_products', [
      {
        productId: 'speed_boost_x2_1hr',
        name: 'Speed Boost x2 1hr',
        description: '2倍速度加成，持续1小时',
        type: 'speed_boost',
        priceUsd: 0.99,
        priceKaia: parseFloat((0.99 / kaiaUsdPrice).toFixed(4)),
        multiplier: 2,
        duration: 1,
        quantity: 1,
        dailyLimit: 1,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        productId: 'speed_boost_x2_12hr',
        name: 'Speed Boost x2 12hr',
        description: '2倍速度加成，持续12小时',
        type: 'speed_boost',
        priceUsd: 3.99,
        priceKaia: parseFloat((3.99 / kaiaUsdPrice).toFixed(4)),
        multiplier: 2,
        duration: 12,
        quantity: 1,
        dailyLimit: 1,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        productId: 'speed_boost_x4_1hr',
        name: 'Speed Boost x4 1hr',
        description: '4倍速度加成，持续1小时',
        type: 'speed_boost',
        priceUsd: 1.99,
        priceKaia: parseFloat((1.99 / kaiaUsdPrice).toFixed(4)),
        multiplier: 4,
        duration: 1,
        quantity: 1,
        dailyLimit: 1,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        productId: 'speed_boost_x4_24hr',
        name: 'Speed Boost x4 24hr',
        description: '4倍速度加成，持续24小时',
        type: 'speed_boost',
        priceUsd: 7.99,
        priceKaia: parseFloat((7.99 / kaiaUsdPrice).toFixed(4)),
        multiplier: 4,
        duration: 24,
        quantity: 1,
        dailyLimit: 1,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        productId: 'time_warp_1hr',
        name: 'Time Warp 1hr',
        description: '时间跳跃1小时，立即获得收益',
        type: 'time_warp',
        priceUsd: 0.69,
        priceKaia: parseFloat((0.69 / kaiaUsdPrice).toFixed(4)),
        multiplier: 1,
        duration: 1,
        quantity: 1,
        dailyLimit: 1,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        productId: 'time_warp_6hr',
        name: 'Time Warp 6hr',
        description: '时间跳跃6小时，立即获得收益',
        type: 'time_warp',
        priceUsd: 2.49,
        priceKaia: parseFloat((2.49 / kaiaUsdPrice).toFixed(4)),
        multiplier: 1,
        duration: 6,
        quantity: 1,
        dailyLimit: 1,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        productId: 'time_warp_24hr',
        name: 'Time Warp 24hr',
        description: '时间跳跃24小时，立即获得收益',
        type: 'time_warp',
        priceUsd: 5.99,
        priceKaia: parseFloat((5.99 / kaiaUsdPrice).toFixed(4)),
        multiplier: 1,
        duration: 24,
        quantity: 1,
        dailyLimit: 1,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        productId: 'vip_membership_30d',
        name: 'VIP Membership',
        description: 'VIP会员1个月，享受30%出货线速度加成、20%出货线价格加成和30%牧场区生产速度加成',
        type: 'vip_membership',
        priceUsd: 8.99,
        priceKaia: parseFloat((8.99 / kaiaUsdPrice).toFixed(4)),
        quantity: 1,
        config: JSON.stringify({
          durationDays: 30,
          deliverySpeedBonus: 0.3, // 30%出货线速度加成
          blockPriceBonus: 0.2, // 20%出货线价格加成
          productionSpeedBonus: 0.3, // 30%牧场区生产速度加成
          benefits: ['delivery_speed_boost', 'block_price_boost', 'production_speed_boost']
        }),
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        productId: 'special_offer_bundle',
        name: 'Special Offer',
        description: '特殊套餐：Time Warp 24hr x2（直接跳转48小时）+ Speed Boost x4 24hr x2，限购一账号一次',
        type: 'special_offer',
        priceUsd: 19.99,
        priceKaia: parseFloat((19.99 / kaiaUsdPrice).toFixed(4)),
        quantity: 1,
        accountLimit: 1,
        config: JSON.stringify({
          bundle: [
            { type: 'time_warp', multiplier: 1, duration: 24, quantity: 2, autoUse: true },
            { type: 'speed_boost', multiplier: 4, duration: 24, quantity: 2, autoUse: false }
          ]
        }),
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ]);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.bulkDelete('iap_products', null, {});
  }
};