// 测试累积离线奖励功能
const axios = require('axios');

const BASE_URL = 'http://localhost:3456';
const JWT_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************.NJ3RM_PzHkmU5BPkqmSTPweMnjqhegFqeCko6lyH2Fg';

const config = {
  headers: {
    'Authorization': `Bearer ${JWT_TOKEN}`,
    'Content-Type': 'application/json'
  }
};

function sleep(seconds) {
  return new Promise(resolve => setTimeout(resolve, seconds * 1000));
}

async function testAccumulatedOfflineRewards() {
  console.log('🧪 测试累积离线奖励功能...');
  console.log('='.repeat(60));
  
  try {
    // 第一步：获取当前离线奖励状态
    console.log('📋 第一步：获取当前离线奖励状态...');
    const getRewardResponse = await axios.get(`${BASE_URL}/api/wallet/offline-reward`, config);
    
    console.log('当前离线奖励状态:');
    console.log('   是否离线:', getRewardResponse.data.data.isOffline);
    console.log('   离线时间:', getRewardResponse.data.data.offlineTime, '秒');
    console.log('   累积宝石奖励:', getRewardResponse.data.data.offlineReward.gem);
    console.log('   累积奖励:', getRewardResponse.data.data.offlineReward.accumulatedGems);
    
    // 第二步：如果有累积奖励，先领取
    if (getRewardResponse.data.data.offlineReward.gem > 0) {
      console.log('\n📋 第二步：领取当前累积奖励...');
      const claimResponse = await axios.post(`${BASE_URL}/api/wallet/claim-offline-reward`, {}, config);
      
      console.log('领取结果:');
      console.log('   领取的宝石:', claimResponse.data.data.claimedReward.gem);
      console.log('   剩余奖励:', claimResponse.data.data.remainingReward.gem);
    }
    
    // 第三步：等待一段时间让奖励累积
    console.log('\n📋 第三步：等待130秒让奖励累积（超过2分钟离线阈值）...');
    await sleep(130);

    // 第四步：再次获取离线奖励状态（应该有新的累积奖励）
    console.log('\n📋 第四步：获取新的累积奖励状态...');
    const newRewardResponse = await axios.get(`${BASE_URL}/api/wallet/offline-reward`, config);
    
    console.log('新的离线奖励状态:');
    console.log('   是否离线:', newRewardResponse.data.data.isOffline);
    console.log('   离线时间:', newRewardResponse.data.data.offlineTime, '秒');
    console.log('   累积宝石奖励:', newRewardResponse.data.data.offlineReward.gem);
    console.log('   累积奖励:', newRewardResponse.data.data.offlineReward.accumulatedGems);
    
    // 第五步：不领取奖励，再等待一段时间
    console.log('\n📋 第五步：不领取奖励，再等待130秒...');
    await sleep(130);
    
    // 第六步：再次获取离线奖励状态（奖励应该继续累积）
    console.log('\n📋 第六步：获取继续累积的奖励状态...');
    const finalRewardResponse = await axios.get(`${BASE_URL}/api/wallet/offline-reward`, config);
    
    console.log('继续累积后的离线奖励状态:');
    console.log('   是否离线:', finalRewardResponse.data.data.isOffline);
    console.log('   离线时间:', finalRewardResponse.data.data.offlineTime, '秒');
    console.log('   累积宝石奖励:', finalRewardResponse.data.data.offlineReward.gem);
    console.log('   累积奖励:', finalRewardResponse.data.data.offlineReward.accumulatedGems);
    
    // 第七步：最终领取所有累积奖励
    console.log('\n📋 第七步：领取所有累积奖励...');
    const finalClaimResponse = await axios.post(`${BASE_URL}/api/wallet/claim-offline-reward`, {}, config);
    
    console.log('最终领取结果:');
    console.log('   领取的宝石:', finalClaimResponse.data.data.claimedReward.gem);
    console.log('   剩余奖励:', finalClaimResponse.data.data.remainingReward.gem);
    
    // 分析累积效果
    console.log('\n🔍 累积离线奖励功能分析:');
    const step4Gems = newRewardResponse.data.data.offlineReward.gem || 0;
    const step6Gems = finalRewardResponse.data.data.offlineReward.gem || 0;
    
    if (step6Gems > step4Gems) {
      console.log('✅ 累积功能正常！奖励从', step4Gems, '增加到', step6Gems);
      console.log('✅ 用户不领取奖励时，奖励会持续累积');
    } else {
      console.log('❌ 累积功能可能有问题，奖励没有继续增加');
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.response?.data || error.message);
  }
}

testAccumulatedOfflineRewards();
