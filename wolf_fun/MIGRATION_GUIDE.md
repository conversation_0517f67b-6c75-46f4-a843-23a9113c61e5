# 累积离线奖励功能 - 数据库迁移指南

## 📋 概述

本指南介绍如何使用 Sequelize 迁移文件为累积离线奖励功能添加必要的数据库结构。

## 🗂️ 文件结构

```
├── migrations/
│   ├── 20250714-add-accumulated-offline-rewards.js  # 主迁移文件
│   └── README.md                                    # 迁移文档
├── run_sequelize_migration.js                       # 执行迁移脚本
├── rollback_sequelize_migration.js                  # 回滚迁移脚本
├── verify_migration.js                              # 验证迁移脚本
└── MIGRATION_GUIDE.md                               # 本指南
```

## 🚀 快速开始

### 1. 执行迁移

```bash
# 使用 npm 脚本（推荐）
npm run migrate:offline-rewards

# 或直接运行脚本
node run_sequelize_migration.js
```

### 2. 验证迁移

```bash
# 验证迁移是否成功
npm run migrate:offline-rewards:verify
```

### 3. 回滚迁移（如需要）

```bash
# 回滚迁移
npm run migrate:offline-rewards:rollback
```

## 📊 迁移内容

### 新增字段

| 字段名 | 类型 | 默认值 | 允许空值 | 说明 |
|--------|------|--------|----------|------|
| `accumulatedOfflineGems` | DECIMAL(65,3) | 0.000 | 否 | 累积的离线宝石奖励 |
| `lastOfflineRewardCalculation` | DATETIME | NULL | 是 | 上次离线奖励计算时间 |

### 新增索引

| 索引名 | 字段 | 用途 |
|--------|------|------|
| `idx_user_wallets_accumulated_offline_gems` | accumulatedOfflineGems | 提高累积奖励查询性能 |
| `idx_user_wallets_last_offline_calculation` | lastOfflineRewardCalculation | 提高时间查询性能 |

## 🔧 迁移特性

### ✅ 智能检测
- 自动检测字段和索引是否已存在
- 避免重复创建导致的错误
- 支持安全的重复执行

### ✅ 事务安全
- 所有操作在数据库事务中执行
- 失败时自动回滚
- 确保数据一致性

### ✅ 详细日志
- 提供清晰的执行日志
- 显示每个步骤的状态
- 便于问题排查

## 📝 使用示例

### 执行迁移示例输出

```bash
$ npm run migrate:offline-rewards

🔧 开始执行 Sequelize 迁移...
✅ 数据库连接成功
📝 执行迁移：添加累积离线奖励字段...
accumulatedOfflineGems 字段已存在，跳过
lastOfflineRewardCalculation 字段已存在，跳过
accumulatedOfflineGems 索引已存在，跳过
lastOfflineRewardCalculation 索引已存在，跳过
✅ 迁移完成
🎉 迁移执行成功！
```

### 验证迁移示例输出

```bash
$ npm run migrate:offline-rewards:verify

🔍 开始验证迁移结果...
✅ 数据库连接成功

📋 检查字段是否存在...
✅ accumulatedOfflineGems 字段存在
✅ lastOfflineRewardCalculation 字段存在

📋 检查索引是否存在...
✅ 索引 idx_user_wallets_accumulated_offline_gems 存在
✅ 索引 idx_user_wallets_last_offline_calculation 存在

🎊 所有验证项目都通过！
```

## ⚠️ 注意事项

### 环境要求
- Node.js >= 22.0.0
- MySQL 数据库
- 正确配置的 `.env` 文件

### 数据安全
- 迁移前建议备份数据库
- 在测试环境先验证迁移
- 生产环境执行前确认无误

### 性能影响
- 新增索引会略微增加写入开销
- 但会显著提高查询性能
- 对现有功能无影响

## 🔍 故障排除

### 常见问题

1. **字段已存在错误**
   ```
   Error: Duplicate column name 'accumulatedOfflineGems'
   ```
   - 解决方案：这是正常的，迁移脚本会自动处理

2. **连接失败**
   ```
   Error: Access denied for user
   ```
   - 检查 `.env` 文件中的数据库配置
   - 确认数据库用户权限

3. **索引创建失败**
   ```
   Error: Duplicate key name
   ```
   - 索引可能已存在，这是正常的

### 手动验证

如果需要手动验证迁移结果：

```sql
-- 检查字段
SHOW COLUMNS FROM user_wallets LIKE 'accumulatedOfflineGems';
SHOW COLUMNS FROM user_wallets LIKE 'lastOfflineRewardCalculation';

-- 检查索引
SHOW INDEX FROM user_wallets WHERE Key_name LIKE '%offline%';
```

## 📞 支持

如果遇到问题：
1. 首先运行验证脚本确认状态
2. 检查日志输出中的错误信息
3. 确认环境配置正确
4. 必要时可以安全地重复执行迁移

---

**注意**: 这些迁移文件是为累积离线奖励功能专门设计的，与标准的 Sequelize CLI 迁移系统独立运行。
