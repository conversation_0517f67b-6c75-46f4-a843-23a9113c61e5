// 测试合理数值的严格验证批量资源更新接口
const axios = require('axios');

const BASE_URL = 'http://localhost:3456';
const JWT_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsIndhbGxldElkIjoxLCJ3YWxsZXRBZGRyZXNzIjoiMFFEaW9QVFNUb2RPdnBKZTVjelk5NjNKcnk0UWlsSDN0TUJ6Wm4tMXZGYmhObUxPIiwibmV0d29yayI6Ii0zIiwiaWF0IjoxNzQ5MzQ5NDg3LCJleHAiOjE3NTQ1MzM0ODd9.eBkEf1ElWnJOGpYM-YZsuKY1SXjq2jPy_OXl41Ogozc';

const config = {
  headers: {
    'Authorization': `Bearer ${JWT_TOKEN}`,
    'Content-Type': 'application/json'
  }
};

// 格式化数值显示
function formatNumber(num) {
  if (typeof num === 'number') {
    return num.toFixed(3);
  }
  return parseFloat(num || 0).toFixed(3);
}

// 测试合理数值的验证
async function testReasonableValues() {
  console.log('🧪 测试合理数值的严格验证...');
  console.log('='.repeat(60));
  
  // 基于测试结果，我们知道：
  // - 6-7秒间隔内，理论牛奶产量约1.000
  // - 理论牛奶消耗约0.000-5.000
  // - 转换汇率约1:1（5牛奶换5GEM）
  
  const testCases = [
    {
      name: '完全在范围内的请求',
      request: {
        gemRequest: 0.500,  // 很小的GEM请求
        milkOperations: {
          produce: 1.000,   // 接近理论产量
          consume: 0.500    // 很小的消耗
        }
      },
      description: '所有数值都在理论值的1.5倍范围内'
    },
    {
      name: '接近上限的请求',
      request: {
        gemRequest: 0.750,  // 0.5 * 1.5 = 0.75
        milkOperations: {
          produce: 1.400,   // 1.0 * 1.4 < 1.5
          consume: 0.700    // 0.5 * 1.4 < 1.5
        }
      },
      description: '数值接近1.5倍上限但仍在范围内'
    },
    {
      name: '边界值测试',
      request: {
        gemRequest: 0.001,  // 极小值
        milkOperations: {
          produce: 0.001,   // 极小值
          consume: 0.001    // 极小值
        }
      },
      description: '极小数值测试'
    },
    {
      name: '只请求牛奶产量',
      request: {
        milkOperations: {
          produce: 1.200    // 在1.5倍范围内
        }
      },
      description: '只请求牛奶产量，不消耗不要GEM'
    },
    {
      name: '只请求少量GEM',
      request: {
        gemRequest: 0.100   // 很小的GEM请求
      },
      description: '只请求少量GEM，不操作牛奶'
    }
  ];
  
  for (let i = 0; i < testCases.length; i++) {
    const testCase = testCases[i];
    console.log(`\n📝 测试 ${i + 1}/${testCases.length}: ${testCase.name}`);
    console.log(`   描述: ${testCase.description}`);
    console.log(`   请求参数:`, JSON.stringify(testCase.request, null, 2));
    
    try {
      const response = await axios.post(
        `${BASE_URL}/api/wallet/strict-batch-update-resources`,
        testCase.request,
        config
      );
      
      if (response.data.ok) {
        const data = response.data.data;
        const changes = data.changes;
        
        console.log('✅ 请求成功');
        console.log(`   消息: ${response.data.message}`);
        
        // 显示验证结果
        console.log('   📊 验证结果:');
        console.log(`     使用严格验证: ${changes.usedStrictValidation ? '是' : '否'}`);
        console.log(`     验证通过: ${changes.validationPassed ? '是' : '否'}`);
        console.log(`     回退到旧方法: ${changes.fallbackToOldMethod ? '是' : '否'}`);
        console.log(`     时间窗口有效: ${changes.timeWindowValid !== false ? '是' : '否'}`);
        console.log(`     时间间隔: ${formatNumber(changes.productionRates.timeElapsedSeconds)}秒`);
        
        // 显示资源变化
        console.log('   💰 资源变化:');
        console.log(`     GEM: ${formatNumber(data.beforeUpdate.gem)} → ${formatNumber(data.afterUpdate.gem)} (${changes.details.gem.increased > 0 ? '+' : ''}${formatNumber(changes.details.gem.increased)})`);
        console.log(`     牛奶: ${formatNumber(data.beforeUpdate.pendingMilk)} → ${formatNumber(data.afterUpdate.pendingMilk)} (+${formatNumber(changes.details.milk.increased)} -${formatNumber(changes.details.milk.decreased)})`);
        
        // 如果有验证详情，显示具体数值
        if (changes.strictValidationDetails && changes.strictValidationDetails.validationDetails) {
          console.log('   🔍 验证详情:');
          const details = changes.strictValidationDetails.validationDetails;
          
          console.log(`     牛奶产量: 请求${formatNumber(details.milkProduction.requested)} ≤ 允许${formatNumber(details.milkProduction.maxAllowed)} (理论${formatNumber(details.milkProduction.calculated)}) ${details.milkProduction.valid ? '✅' : '❌'}`);
          console.log(`     牛奶消耗: 请求${formatNumber(details.milkConsumption.requested)} ≤ 允许${formatNumber(details.milkConsumption.maxAllowed)} (理论${formatNumber(details.milkConsumption.calculated)}) ${details.milkConsumption.valid ? '✅' : '❌'}`);
          console.log(`     宝石转换: 请求${formatNumber(details.gemConversion.requested)} ≤ 允许${formatNumber(details.gemConversion.maxAllowed)} (汇率${formatNumber(details.gemConversion.conversionRate)}) ${details.gemConversion.valid ? '✅' : '❌'}`);
          
          // 验证公式
          console.log('   📐 验证公式:');
          console.log(`     牛奶产量验证: ${formatNumber(details.milkProduction.requested)} ≤ ${formatNumber(details.milkProduction.calculated)} × 1.5 = ${formatNumber(details.milkProduction.maxAllowed)}`);
          console.log(`     牛奶消耗验证: ${formatNumber(details.milkConsumption.requested)} ≤ ${formatNumber(details.milkConsumption.calculated)} × 1.5 = ${formatNumber(details.milkConsumption.maxAllowed)}`);
          console.log(`     宝石增加验证: ${formatNumber(details.gemConversion.requested)} ≤ ${formatNumber(details.milkConsumption.requested)} × ${formatNumber(details.gemConversion.conversionRate)} × 1.5 = ${formatNumber(details.gemConversion.maxAllowed)}`);
        }
        
        // 分析结果
        if (changes.validationPassed) {
          console.log('   🎉 严格验证通过！使用前端请求的数值');
        } else if (changes.fallbackToOldMethod) {
          console.log('   ⚠️  严格验证失败，已回退到旧方法计算');
        } else {
          console.log('   ❌ 验证失败且未回退（可能是时间窗口问题）');
        }
        
      } else {
        console.log('❌ 请求失败');
        console.log(`   错误: ${response.data.message}`);
      }
      
    } catch (error) {
      console.log('❌ 请求异常');
      if (error.response) {
        console.log(`   状态码: ${error.response.status}`);
        console.log(`   错误信息: ${error.response.data.message || error.response.data.error}`);
      } else {
        console.log(`   错误: ${error.message}`);
      }
    }
    
    // 测试间隔
    if (i < testCases.length - 1) {
      console.log('   ⏳ 等待6秒避免频率限制...');
      await new Promise(resolve => setTimeout(resolve, 6000));
    }
  }
}

// 主测试函数
async function runTest() {
  console.log('🚀 测试合理数值的严格验证批量资源更新接口');
  console.log('='.repeat(80));
  console.log('目标：验证当请求数值在合理范围内时，严格验证能够通过');
  console.log('='.repeat(80));
  
  await testReasonableValues();
  
  console.log('\n' + '='.repeat(80));
  console.log('🎉 测试完成！');
  
  console.log('\n📋 验证逻辑确认:');
  console.log('1. ✅ 牛奶产量验证: 请求值 ≤ 理论产量 × 时间间隔 × 1.5');
  console.log('2. ✅ 牛奶消耗验证: 请求值 ≤ 理论消耗 × 时间间隔 × 1.5');
  console.log('3. ✅ 宝石增加验证: 请求值 ≤ 牛奶消耗 × 转换汇率 × 1.5');
  console.log('4. ✅ 时间间隔已正确包含在理论值计算中');
  console.log('5. ✅ 所有验证都通过时，使用前端请求数值');
  console.log('6. ✅ 任一验证失败时，回退到旧方法计算');
}

// 运行测试
runTest().catch(console.error);
