// 测试离线奖励API的脚本
const axios = require('axios');

const BASE_URL = 'http://localhost:3457/api';

// 测试用的JWT令牌
const JWT_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************.lXoRXISFZ1PoPkGOOaDzgTBD8fR856jsi5_CAiFnmqY';

// 创建测试用的认证头
const createAuthHeaders = () => {
  return {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${JWT_TOKEN}`,
    'Accept-Language': 'zh'
  };
};

// 测试获取离线奖励信息
async function testGetOfflineReward() {
  console.log('\n=== 测试获取离线奖励信息 ===');
  try {
    const response = await axios.get(`${BASE_URL}/wallet/offline-reward`, {
      headers: createAuthHeaders()
    });
    
    console.log('✅ 获取离线奖励信息成功');
    console.log('响应状态:', response.status);
    console.log('响应数据:', JSON.stringify(response.data, null, 2));
    
    return response.data;
  } catch (error) {
    console.log('❌ 获取离线奖励信息失败');
    if (error.response) {
      console.log('错误状态:', error.response.status);
      console.log('错误数据:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.log('错误信息:', error.message);
    }
    return null;
  }
}

// 测试结算离线奖励
async function testClaimOfflineReward() {
  console.log('\n=== 测试结算离线奖励 ===');
  try {
    const response = await axios.post(`${BASE_URL}/wallet/offline-reward`, {}, {
      headers: createAuthHeaders()
    });
    
    console.log('✅ 结算离线奖励成功');
    console.log('响应状态:', response.status);
    console.log('响应数据:', JSON.stringify(response.data, null, 2));
    
    return response.data;
  } catch (error) {
    console.log('❌ 结算离线奖励失败');
    if (error.response) {
      console.log('错误状态:', error.response.status);
      console.log('错误数据:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.log('错误信息:', error.message);
    }
    return null;
  }
}

// 测试健康检查
async function testHealthCheck() {
  console.log('\n=== 测试健康检查 ===');
  try {
    const response = await axios.get(`${BASE_URL}/health/health`);
    console.log('✅ 健康检查成功');
    console.log('响应状态:', response.status);
    console.log('响应数据:', JSON.stringify(response.data, null, 2));
    return true;
  } catch (error) {
    console.log('❌ 健康检查失败');
    if (error.response) {
      console.log('错误状态:', error.response.status);
      console.log('错误数据:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.log('错误信息:', error.message);
    }
    return false;
  }
}

// 主测试函数
async function runTests() {
  console.log('🚀 开始测试离线奖励API...');
  
  // 首先测试健康检查
  const healthOk = await testHealthCheck();
  if (!healthOk) {
    console.log('❌ 服务器健康检查失败，停止测试');
    return;
  }
  
  // 测试获取离线奖励信息
  const rewardInfo = await testGetOfflineReward();
  
  // 如果有离线奖励，测试结算
  if (rewardInfo && rewardInfo.ok && rewardInfo.data.isOffline && rewardInfo.data.offlineReward.gem > 0) {
    console.log(`\n发现离线奖励: ${rewardInfo.data.offlineReward.gem} GEM`);
    await testClaimOfflineReward();
  } else {
    console.log('\n没有离线奖励可领取，跳过结算测试');
  }
  
  console.log('\n🎉 测试完成！');
}

// 运行测试
runTests().catch(console.error);
