// test_excel_reader.js - 测试Excel读取功能
const XLSX = require('xlsx');
const fs = require('fs');
const path = require('path');

async function testExcelReader() {
  try {
    console.log('🔍 开始测试Excel文件读取...');
    
    // 读取Excel文件
    const excelPath = path.join(__dirname, 'doc/区域升级.xlsx');
    
    if (!fs.existsSync(excelPath)) {
      console.error('❌ Excel文件不存在:', excelPath);
      return;
    }
    
    console.log('📁 读取文件:', excelPath);
    
    // 读取Excel文件
    const workbook = XLSX.readFile(excelPath);
    
    // 获取所有工作表名称
    const sheetNames = workbook.SheetNames;
    console.log('📋 工作表列表:', sheetNames);
    
    // 读取所有工作表的数据
    const sheetsData = {};
    
    sheetNames.forEach((sheetName, index) => {
      console.log(`\n📊 处理工作表 ${index + 1}: ${sheetName}`);
      
      const worksheet = workbook.Sheets[sheetName];
      
      // 获取工作表范围
      const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1:A1');
      console.log(`   范围: ${worksheet['!ref'] || 'A1:A1'}`);
      
      // 转换为JSON格式（使用第一行作为标题）
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { 
        header: 1, // 使用数组格式
        defval: '' // 空单元格的默认值
      });
      
      sheetsData[sheetName] = jsonData;
      
      console.log(`   数据行数: ${jsonData.length}`);
      
      // 显示前几行数据
      if (jsonData.length > 0) {
        console.log('   前3行数据:');
        jsonData.slice(0, 3).forEach((row, rowIndex) => {
          console.log(`     行${rowIndex + 1}:`, row);
        });
      }
    });
    
    // 生成解析结果
    const parsedData = parseRegionUpgradeData(sheetsData);
    
    console.log('\n📈 解析结果汇总:');
    console.log('   总工作表数:', parsedData.summary.totalSheets);
    console.log('   总数据行数:', parsedData.summary.totalRegions);
    console.log('   处理时间:', parsedData.summary.processedAt);
    
    // 显示解析后的区域数据
    parsedData.regions.forEach((region, index) => {
      console.log(`\n🏗️  区域 ${index + 1} (${region.sheetName}):`);
      console.log('   标题行:', region.headers);
      console.log('   数据行数:', region.totalRows);
      
      if (region.data.length > 0) {
        console.log('   示例数据:', region.data[0]);
      }
    });
    
    console.log('\n✅ Excel文件读取测试完成!');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error(error.stack);
  }
}

// 解析区域升级数据的函数（与控制器中的相同）
function parseRegionUpgradeData(sheetsData) {
  const result = {
    regions: [],
    summary: {}
  };

  // 遍历所有工作表
  Object.keys(sheetsData).forEach(sheetName => {
    const sheetData = sheetsData[sheetName];
    
    if (sheetData.length === 0) return;

    // 假设第一行是标题行
    const headers = sheetData[0];
    const dataRows = sheetData.slice(1);

    // 解析数据行
    const regionData = dataRows.map((row, index) => {
      const rowData = {};
      headers.forEach((header, colIndex) => {
        if (header && header.trim && header.trim()) {
          rowData[header.trim()] = row[colIndex] || '';
        }
      });
      rowData._rowIndex = index + 2; // Excel行号（从2开始，因为第1行是标题）
      return rowData;
    }).filter(row => {
      // 过滤掉完全空的行
      return Object.values(row).some(value => value !== '' && value !== null && value !== undefined);
    });

    result.regions.push({
      sheetName: sheetName,
      headers: headers.filter(h => h && h.trim && h.trim()),
      data: regionData,
      totalRows: regionData.length
    });
  });

  // 生成汇总信息
  result.summary = {
    totalSheets: Object.keys(sheetsData).length,
    totalRegions: result.regions.reduce((sum, region) => sum + region.totalRows, 0),
    processedAt: new Date().toISOString()
  };

  return result;
}

// 运行测试
if (require.main === module) {
  testExcelReader();
}

module.exports = { testExcelReader };
