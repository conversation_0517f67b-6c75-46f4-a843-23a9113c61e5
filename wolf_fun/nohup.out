personalKolRewardWorker.ts loaded
teamKolRewardWorker.ts loaded
[BullMQ] 正在初始化 Redis 连接...
[BullMQ] Redis 连接已初始化，正在创建任务队列...
[BullMQ] 任务队列已创建完成
[LotteryWorker] 模块初始化开始...
[LotteryWorker] 启动 Lottery Worker...
[LotteryWorker] Lottery Worker 初始化完成，准备处理任务。
moofHoldersRewardWorker.ts loaded
dailyRebateSettlementWorker.ts loaded
jackpotChestWorker.ts loaded
[WithdrawalWorker] TON提现工作器初始化开始...
kaiapriceUpdateWorker.ts loaded
(node:89587) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
Connected to Redis.
[BullMQ] Redis 已连接
[BullMQ] Redis 准备就绪
[LotteryWorker] 奖池初始化完成
winerNum: 1
Latest block number: 48301681
MySQL connection has been established successfully.
数据库连接成功
数据库连接成功
Redis test value: hello
[DailySessions] 立刻执行预生成当天 Session 记录...
[
  {
    session_category: '12:00:00',
    session_dt: 2025-05-29T04:00:00.000Z
  },
  {
    session_category: '20:00:00',
    session_dt: 2025-05-29T12:00:00.000Z
  }
]
每日 Session 预生成任务已启动。
[ScheduleJobs] 开始调度每日回合任务...
[ScheduleJobs] 正在查询 2025-05-29 的场次...
Wolf.Fun game server running on port 3456...
[ScheduleJobs] 找到 2 个场次
[ScheduleJobs] 正在处理场次: 12:00:00
[ScheduleJobs] 正在设置任务: scheduler-12:00:00-round-1, CRON: 0 8 12 * * *
[DailySessions] Session (12:00:00 2025-05-29T12:00:00+08:00) 已存在，跳过创建。
[DailySessions] Session (20:00:00 2025-05-29T20:00:00+08:00) 已存在，跳过创建。
[ScheduleJobs] 任务设置成功: scheduler-12:00:00-round-1
[ScheduleJobs] 正在设置任务: scheduler-12:00:00-round-2, CRON: 0 18 12 * * *
[ScheduleJobs] 任务设置成功: scheduler-12:00:00-round-2
[ScheduleJobs] 正在设置任务: scheduler-12:00:00-round-3, CRON: 0 28 12 * * *
[ScheduleJobs] 任务设置成功: scheduler-12:00:00-round-3
[ScheduleJobs] 正在处理场次: 20:00:00
[ScheduleJobs] 正在设置任务: scheduler-20:00:00-round-1, CRON: 0 8 20 * * *
[LotteryWorker] Worker 接收到新任务: ID=repeat:scheduler-12:00:00-round-1:1748528561173，数据: { session_category: '12:00:00', roundIndex: 1 }
[LotteryWorker] 开始处理任务 ID: repeat:scheduler-12:00:00-round-1:1748528561173，数据: { session_category: '12:00:00', roundIndex: 1 }
[ScheduleJobs] 任务设置成功: scheduler-20:00:00-round-1
[ScheduleJobs] 正在设置任务: scheduler-20:00:00-round-2, CRON: 0 18 20 * * *
[ScheduleJobs] 任务设置成功: scheduler-20:00:00-round-2
[ScheduleJobs] 正在设置任务: scheduler-20:00:00-round-3, CRON: 0 28 20 * * *
[ScheduleJobs] 任务设置成功: scheduler-20:00:00-round-3
[ScheduleJobs] 所有任务调度完成
所有抽奖任务已调度
[ScheduleKaiaPriceUpdate] 开始设置Kaia价格更新任务定时调度...
[ScheduleKaiaPriceUpdate] 正在设置任务: scheduler-kaia-price-update, CRON: 0 */5 * * * *
[ScheduleKaiaPriceUpdate] Kaia价格更新任务设置成功: scheduler-kaia-price-update
[ScheduleKaiaPriceUpdate] 任务将按照以下计划执行: 0 */5 * * * *
Kaia价格更新任务已调度
正在初始化队列和处理器...
正在初始化各模块处理器...
初始化抽奖结果处理器...
抽奖结果处理器已初始化
初始化 MOOF 持有者奖励处理器...
MOOF 持有者奖励处理器已初始化
初始化个人 KOL 奖励处理器...
个人 KOL 奖励处理器已初始化
初始化团队 KOL 奖励处理器...
团队 KOL 奖励处理器已初始化
初始化每日返利结算处理器...
每日返利结算处理器已初始化
初始化 Jackpot 宝箱处理器...
Jackpot 宝箱处理器已初始化
初始化提现处理器...
提现处理器已初始化
初始化KAIA价格更新处理器...
KAIA价格更新处理器已初始化
已添加 Jackpot 奖池初始化任务
[KaiaPriceUpdateWorker] 开始处理Kaia价格更新任务 ID: repeat:scheduler-kaia-price-update:1748528561187
[KaiaPriceUpdateWorker] 正在从Kaiascan API获取Kaia价格...
已添加 Jackpot 自动领取定时任务
[LotteryWorker] 获取到最新的 seqno: 48301681
[LotteryWorker] 找到场次: sessionId 63
[LotteryWorker] 未找到未处理的房间，sessionId: 63，roundIndex: 1
[LotteryWorker] 任务 repeat:scheduler-12:00:00-round-1:1748528561173 已完成。
[LotteryWorker] Worker 接收到新任务: ID=repeat:scheduler-12:00:00-round-2:1748528561177，数据: { session_category: '12:00:00', roundIndex: 2 }
[LotteryWorker] 开始处理任务 ID: repeat:scheduler-12:00:00-round-2:1748528561177，数据: { session_category: '12:00:00', roundIndex: 2 }
[KaiaPriceUpdateWorker] 获取到Kaia价格: 1 KAIA = 0.1133470361765994 USD
[KaiaPriceUpdateWorker] 开始更新所有IapProduct的priceKaia字段...
[KaiaPriceUpdateWorker] 找到 8 个需要更新的产品
[KaiaPriceUpdateWorker] 产品 Speed Boost x2 1hr (ID: 25) 价格已更新: 1.00 USD = 8.8225 KAIA
[KaiaPriceUpdateWorker] 产品 Speed Boost x2 24hr (ID: 26) 价格已更新: 1.00 USD = 8.8225 KAIA
[KaiaPriceUpdateWorker] 产品 Speed Boost x4 1hr (ID: 27) 价格已更新: 1.00 USD = 8.8225 KAIA
[KaiaPriceUpdateWorker] 产品 Speed Boost x4 24hr (ID: 28) 价格已更新: 1.00 USD = 8.8225 KAIA
[KaiaPriceUpdateWorker] 产品 Time Warp 1hr (ID: 29) 价格已更新: 1.00 USD = 8.8225 KAIA
[KaiaPriceUpdateWorker] 产品 Time Warp 24hr (ID: 30) 价格已更新: 1.00 USD = 8.8225 KAIA
[KaiaPriceUpdateWorker] 产品 VIP Membership (ID: 31) 价格已更新: 1.00 USD = 8.8225 KAIA
[KaiaPriceUpdateWorker] 产品 Special Offer (ID: 32) 价格已更新: 1.00 USD = 8.8225 KAIA
[KaiaPriceUpdateWorker] 价格更新完成，共更新了 8 个产品
[KaiaPriceUpdateWorker] Kaia价格更新任务 ID: repeat:scheduler-kaia-price-update:1748528561187 已完成
[KaiaPriceUpdateWorker] Kaia价格更新任务 repeat:scheduler-kaia-price-update:1748528561187 成功完成
[LotteryWorker] 获取到最新的 seqno: 48301681
[LotteryWorker] 找到场次: sessionId 63
[LotteryWorker] 未找到未处理的房间，sessionId: 63，roundIndex: 2
[LotteryWorker] 任务 repeat:scheduler-12:00:00-round-2:1748528561177 已完成。
[LotteryWorker] Worker 接收到新任务: ID=repeat:scheduler-12:00:00-round-3:1748528561178，数据: { session_category: '12:00:00', roundIndex: 3 }
[LotteryWorker] 开始处理任务 ID: repeat:scheduler-12:00:00-round-3:1748528561178，数据: { session_category: '12:00:00', roundIndex: 3 }
[LotteryWorker] 获取到最新的 seqno: 48301681
[LotteryWorker] 找到场次: sessionId 63
[LotteryWorker] 未找到未处理的房间，sessionId: 63，roundIndex: 3
[LotteryWorker] 任务 repeat:scheduler-12:00:00-round-3:1748528561178 已完成。
[LotteryWorker] Worker 接收到新任务: ID=repeat:scheduler-20:00:00-round-1:1748528561182，数据: { session_category: '20:00:00', roundIndex: 1 }
[LotteryWorker] 开始处理任务 ID: repeat:scheduler-20:00:00-round-1:1748528561182，数据: { session_category: '20:00:00', roundIndex: 1 }
[LotteryWorker] 获取到最新的 seqno: 48301682
[LotteryWorker] 找到场次: sessionId 64
[LotteryWorker] 未找到未处理的房间，sessionId: 64，roundIndex: 1
[LotteryWorker] 任务 repeat:scheduler-20:00:00-round-1:1748528561182 已完成。
[LotteryWorker] Worker 接收到新任务: ID=repeat:scheduler-20:00:00-round-2:1748528561185，数据: { session_category: '20:00:00', roundIndex: 2 }
[LotteryWorker] 开始处理任务 ID: repeat:scheduler-20:00:00-round-2:1748528561185，数据: { session_category: '20:00:00', roundIndex: 2 }
[LotteryWorker] 获取到最新的 seqno: 48301682
[LotteryWorker] 找到场次: sessionId 64
[LotteryWorker] 未找到未处理的房间，sessionId: 64，roundIndex: 2
[LotteryWorker] 任务 repeat:scheduler-20:00:00-round-2:1748528561185 已完成。
[LotteryWorker] Worker 接收到新任务: ID=repeat:scheduler-20:00:00-round-3:1748528561186，数据: { session_category: '20:00:00', roundIndex: 3 }
[LotteryWorker] 开始处理任务 ID: repeat:scheduler-20:00:00-round-3:1748528561186，数据: { session_category: '20:00:00', roundIndex: 3 }
[LotteryWorker] 获取到最新的 seqno: 48301682
[LotteryWorker] 找到场次: sessionId 64
[LotteryWorker] 未找到未处理的房间，sessionId: 64，roundIndex: 3
[LotteryWorker] 任务 repeat:scheduler-20:00:00-round-3:1748528561186 已完成。
