// 验证迁移是否成功执行
const { Sequelize } = require('sequelize');
require('dotenv').config();

async function verifyMigration() {
  console.log('🔍 开始验证迁移结果...');
  
  // 创建 Sequelize 实例
  const sequelize = new Sequelize({
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    username: process.env.DB_USER,
    password: process.env.DB_PASS,
    database: process.env.DB_NAME,
    dialect: 'mysql',
    logging: false // 关闭SQL日志
  });

  try {
    // 测试连接
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');

    // 获取 QueryInterface
    const queryInterface = sequelize.getQueryInterface();

    console.log('\n📋 检查字段是否存在...');
    
    // 检查表结构
    const tableDescription = await queryInterface.describeTable('user_wallets');
    
    // 验证 accumulatedOfflineGems 字段
    if (tableDescription.accumulatedOfflineGems) {
      const field = tableDescription.accumulatedOfflineGems;
      console.log('✅ accumulatedOfflineGems 字段存在');
      console.log(`   类型: ${field.type}`);
      console.log(`   允许空值: ${field.allowNull}`);
      console.log(`   默认值: ${field.defaultValue}`);
    } else {
      console.log('❌ accumulatedOfflineGems 字段不存在');
    }

    // 验证 lastOfflineRewardCalculation 字段
    if (tableDescription.lastOfflineRewardCalculation) {
      const field = tableDescription.lastOfflineRewardCalculation;
      console.log('✅ lastOfflineRewardCalculation 字段存在');
      console.log(`   类型: ${field.type}`);
      console.log(`   允许空值: ${field.allowNull}`);
      console.log(`   默认值: ${field.defaultValue}`);
    } else {
      console.log('❌ lastOfflineRewardCalculation 字段不存在');
    }

    console.log('\n📋 检查索引是否存在...');
    
    // 检查索引
    const indexes = await queryInterface.showIndex('user_wallets');
    const indexNames = indexes.map(index => index.name);
    
    // 验证索引
    const expectedIndexes = [
      'idx_user_wallets_accumulated_offline_gems',
      'idx_user_wallets_last_offline_calculation'
    ];

    expectedIndexes.forEach(indexName => {
      if (indexNames.includes(indexName)) {
        console.log(`✅ 索引 ${indexName} 存在`);
        const indexInfo = indexes.find(idx => idx.name === indexName);
        console.log(`   字段: ${indexInfo.fields.map(f => f.attribute).join(', ')}`);
      } else {
        console.log(`❌ 索引 ${indexName} 不存在`);
      }
    });

    console.log('\n📋 测试数据操作...');
    
    // 测试插入和查询操作
    const testQuery = `
      SELECT 
        id,
        accumulatedOfflineGems,
        lastOfflineRewardCalculation
      FROM user_wallets 
      LIMIT 1
    `;
    
    const [results] = await sequelize.query(testQuery);
    
    if (results.length > 0) {
      console.log('✅ 数据查询测试成功');
      console.log('   示例数据:', {
        id: results[0].id,
        accumulatedOfflineGems: results[0].accumulatedOfflineGems,
        lastOfflineRewardCalculation: results[0].lastOfflineRewardCalculation
      });
    } else {
      console.log('⚠️  表中暂无数据，无法测试查询');
    }

    console.log('\n🎉 迁移验证完成！');
    
    // 总结
    const hasAccumulatedField = !!tableDescription.accumulatedOfflineGems;
    const hasCalculationField = !!tableDescription.lastOfflineRewardCalculation;
    const hasAccumulatedIndex = indexNames.includes('idx_user_wallets_accumulated_offline_gems');
    const hasCalculationIndex = indexNames.includes('idx_user_wallets_last_offline_calculation');
    
    console.log('\n📊 验证结果总结:');
    console.log(`   字段 accumulatedOfflineGems: ${hasAccumulatedField ? '✅' : '❌'}`);
    console.log(`   字段 lastOfflineRewardCalculation: ${hasCalculationField ? '✅' : '❌'}`);
    console.log(`   索引 accumulated_offline_gems: ${hasAccumulatedIndex ? '✅' : '❌'}`);
    console.log(`   索引 last_offline_calculation: ${hasCalculationIndex ? '✅' : '❌'}`);
    
    const allSuccess = hasAccumulatedField && hasCalculationField && hasAccumulatedIndex && hasCalculationIndex;
    
    if (allSuccess) {
      console.log('\n🎊 所有验证项目都通过！累积离线奖励功能的数据库结构已就绪。');
    } else {
      console.log('\n⚠️  部分验证项目未通过，请检查迁移是否正确执行。');
    }

  } catch (error) {
    console.error('❌ 验证失败:', error);
  } finally {
    await sequelize.close();
    console.log('🔌 数据库连接已关闭');
  }
}

// 如果直接运行此文件，则执行验证
if (require.main === module) {
  verifyMigration();
}

module.exports = { verifyMigration };
